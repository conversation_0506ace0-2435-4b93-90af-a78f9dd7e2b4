tool_config:
  ocr_language: "eng" # 识别语言，默认为英文
  qps: 10 # 翻译请求速度
  limits: 10 #并发处理数量
llm_config:
  base_url: "http://*************:15555/chat/v1"
  api_key: "iBYcdv999bDCSOyuNjorZvfaMEooeq1gRBHRwP5L7GGLGMqxBXrSntKsHTiP72vc"
  model: "Qwen3-30B-A3B-GPTQ-Int4"
  translate_prompt: "你是一个专业、真实的机器翻译引擎。\n仅输出翻译后的文本，不包括任何其他文本。\n确保忠实于原意，不改变逻辑、细节或语气，完整保留内容中的情感和表达方式。\n如果无法翻译，请直接输出原始内容，不需要多余的内容，也不需要解释其所表达的含义，采用文本到文本的输出方。\n请根据上下文选择适当的口语化表达或正式语气，使译文符合中文语言习惯，且自然流畅。" # 翻译提示词，可以为空
  summarize_prompt: "请根据以下文档内容，请使用简体中文生成一份简洁明了的简报。简报应包括：\n1. 文档的主要主题和目的\n2. 关键信息点和重要数据\n3. 文档的结论或建议（如果有）\n请确保简报内容准确反映原文档的核心信息，并以清晰的结构呈现。" # 简报生成提示词


