{"_comment": "用户可配置参数 - 仅包含允许用户修改的设置", "_warning": "请勿修改核心安全参数，这些参数由系统强制执行", "logging": {"enabled": true, "log_level": "INFO", "log_file": "logs/offline_security.log", "log_rotation": {"max_size_mb": 10, "backup_count": 5}, "events_to_log": {"license_validation": true, "time_anomalies": true, "security_warnings": true, "system_changes": false, "execution_counts": true}}, "alerts": {"enabled": true, "alert_file": "logs/security_alerts.json", "alert_conditions": {"security_score_below_threshold": true, "time_tampering_detected": true, "system_fingerprint_mismatch": true, "proof_chain_validation_failed": true, "execution_limit_exceeded": true}}, "maintenance": {"auto_cleanup": {"enabled": true, "cleanup_interval_hours": 168, "keep_logs_days": 30, "keep_proofs_days": 7}, "health_check": {"enabled": true, "check_interval_hours": 24, "components_to_check": ["time_sources", "system_fingerprint", "proof_chain_integrity", "storage_space"]}}, "user_interface": {"show_detailed_warnings": true, "show_security_score": true, "show_recommendations": true, "language": "zh"}, "performance": {"cache_system_fingerprint": true, "fingerprint_cache_duration_minutes": 60, "enable_fast_mode": false, "max_proof_chain_length": 50}, "compatibility": {"fallback_mode": {"enabled": true, "description": "当高级功能不可用时的降级模式"}, "legacy_support": {"old_license_format": true, "migration_assistance": true}}, "_allowed_modifications_info": {"description": "以下是用户被允许修改的配置路径", "allowed_paths": ["logging.log_level", "logging.enabled", "logging.events_to_log.*", "alerts.enabled", "alerts.alert_conditions.*", "maintenance.auto_cleanup.cleanup_interval_hours", "maintenance.auto_cleanup.keep_logs_days", "maintenance.auto_cleanup.keep_proofs_days", "user_interface.*", "performance.cache_system_fingerprint", "performance.fingerprint_cache_duration_minutes", "performance.enable_fast_mode", "compatibility.fallback_mode.enabled", "compatibility.legacy_support.*"]}, "_security_notice": {"message": "核心安全参数由系统管理，用户无法修改", "protected_areas": ["时间验证最低安全分数", "安全等级定义", "核心安全组件开关", "异常检测权重", "系统指纹验证阈值", "时间证明链验证逻辑"]}}