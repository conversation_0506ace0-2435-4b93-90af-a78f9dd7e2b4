from functools import wraps
from typing import Callable
from core.license_manager import license_manager, LicenseStatus

def require_license(module_id: str):
    """许可证权限装饰器"""
    def decorator(func: Callable):
        @wraps(func)
        def wrapper(*args, **kwargs):
            # 加载许可证
            status, license_info = license_manager.load_license()
            
            # 检查许可证状态
            if status != LicenseStatus.VALID:
                raise PermissionError(f"许可证无效: {license_manager.get_status_message()}")
            
            # 检查模块权限
            if not license_manager.is_module_authorized(module_id):
                authorized_modules = license_manager.get_authorized_modules()
                raise PermissionError(
                    f"模块 '{module_id}' 未被授权。\n"
                    f"当前授权模块: {', '.join(authorized_modules)}"
                )
            
            # 增加执行计数
            if not license_manager.increment_execution_count():
                raise PermissionError("已达到最大执行次数限制")
            
            return func(*args, **kwargs)
        return wrapper
    return decorator

class PermissionManager:
    @staticmethod
    def check_license_status() -> tuple:
        """检查许可证状态"""
        return license_manager.load_license()
    
    @staticmethod
    def get_authorized_modules() -> list:
        """获取授权模块列表"""
        status, _ = license_manager.load_license()
        if status == LicenseStatus.VALID:
            return license_manager.get_authorized_modules()
        return []
    
    @staticmethod
    def get_license_info():
        """获取许可证信息"""
        return license_manager.get_license_info()
    
    @staticmethod
    def get_status_message() -> str:
        """获取状态消息"""
        return license_manager.get_status_message()