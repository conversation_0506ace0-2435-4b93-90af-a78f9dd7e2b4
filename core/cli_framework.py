"""
基于Click的现代CLI框架
支持动态命令注册、许可证验证和国际化
"""

import click
import sys
from functools import wraps
import logging

from .auth import PermissionManager
from core.license_manager import LicenseStatus
from .registry import registry, ModuleInfo
from .exceptions import *

logger = logging.getLogger(__name__)

# 国际化消息
MESSAGES = {
    'zh': {
        'app_description': '离线多功能CLI应用程序 (基于许可证)',
        'license_holder': '许可证持有者',
        'authorized_modules': '授权模块',
        'expiry_date': '到期时间',
        'license_status': '许可证状态',
        'available_commands': '可用命令',
        'no_command_specified': '未指定命令，使用 --help 查看帮助',
        'command_executed': '命令执行完成',
        'execution_failed': '命令执行失败',
    },
    'en': {
        'app_description': 'Offline Multi-functional CLI Application (License-based)',
        'license_holder': 'License Holder',
        'authorized_modules': 'Authorized Modules',
        'expiry_date': 'Expiry Date',
        'license_status': 'License Status',
        'available_commands': 'Available Commands',
        'no_command_specified': 'No command specified, use --help for help',
        'command_executed': 'Command executed successfully',
        'execution_failed': 'Command execution failed',
    }
}

class CLIContext:
    """CLI上下文管理"""
    
    def __init__(self, language='zh'):
        self.language = language
        self.license_status = None
        self.license_info = None
        self.authorized_modules = []
        self._load_license_info()
    
    def _load_license_info(self):
        """加载许可证信息"""
        try:
            self.license_status, self.license_info = PermissionManager.check_license_status()
            if self.license_status == LicenseStatus.VALID:
                self.authorized_modules = PermissionManager.get_authorized_modules()
        except Exception as e:
            logger.error(f"Failed to load license info: {e}")
    
    def get_message(self, key: str) -> str:
        """获取本地化消息"""
        return MESSAGES.get(self.language, MESSAGES['zh']).get(key, key)
    
    def is_module_authorized(self, module_id: str) -> bool:
        """检查模块是否授权"""
        return module_id in self.authorized_modules

def require_valid_license(f):
    """许可证验证装饰器（用于Click命令）"""
    @wraps(f)
    def wrapper(*args, **kwargs):
        ctx = click.get_current_context()
        cli_ctx = ctx.obj
        
        if cli_ctx.license_status != LicenseStatus.VALID:
            status_msg = PermissionManager.get_status_message()
            click.echo(click.style(f"错误: {status_msg}", fg='red'), err=True)
            ctx.exit(1)
        
        return f(*args, **kwargs)
    return wrapper

def require_module_auth(module_id: str):
    """模块授权验证装饰器"""
    def decorator(f):
        @wraps(f)
        def wrapper(*args, **kwargs):
            ctx = click.get_current_context()
            cli_ctx = ctx.obj
            
            if not cli_ctx.is_module_authorized(module_id):
                error_msg = f"模块 '{module_id}' 未被授权\n当前授权模块: {', '.join(cli_ctx.authorized_modules)}"
                click.echo(click.style(error_msg, fg='red'), err=True)
                ctx.exit(5)
            
            # 检查执行次数限制
            try:
                from core.license_manager import license_manager
                if not license_manager.increment_execution_count():
                    click.echo(click.style("已达到最大执行次数限制", fg='red'), err=True)
                    ctx.exit(6)
            except Exception as e:
                logger.error(f"Failed to increment execution count: {e}")
                click.echo(click.style("许可证验证失败", fg='red'), err=True)
                ctx.exit(1)
            
            return f(*args, **kwargs)
        return wrapper
    return decorator

class DynamicCLI:
    """动态CLI管理器"""
    
    def __init__(self, language='zh'):
        self.language = language
        self.cli_context = CLIContext(language)
        self._setup_main_group()
    
    def _setup_main_group(self):
        """设置主命令组"""
        @click.group(invoke_without_command=True)
        @click.option('--version', is_flag=True, help='显示版本信息')
        # @click.option('--lang', default='zh', type=click.Choice(['zh', 'en']), help='语言设置')
        @click.pass_context
        def cli(ctx, version):
            """离线多功能CLI应用程序 (基于许可证)"""
            ctx.ensure_object(dict)
            ctx.obj = CLIContext('zh')
            
            if version:
                click.echo("CLI Application v1.0.0")
                return
            
            if ctx.invoked_subcommand is None:
                self._show_status_and_help(ctx)
        
        self.main_group = cli
    
    def _show_status_and_help(self, ctx):
        """显示状态信息和帮助"""
        cli_ctx = ctx.obj
        
        # 显示许可证状态
        click.echo(click.style("=== 许可证状态 ===", fg='blue', bold=True))
        
        if cli_ctx.license_status == LicenseStatus.VALID:
            click.echo(click.style("✓ 许可证有效", fg='green'))
            if cli_ctx.license_info:
                click.echo(f"持有者: {cli_ctx.license_info.organization}")
                click.echo(f"到期时间: {cli_ctx.license_info.end_date}")
                click.echo(f"授权模块: {', '.join(cli_ctx.authorized_modules)}")
        else:
            status_msg = PermissionManager.get_status_message()
            click.echo(click.style(f"✗ {status_msg}", fg='red'))
        
        click.echo()
        
        # 显示可用命令
        if cli_ctx.authorized_modules:
            click.echo(click.style("=== 可用命令 ===", fg='blue', bold=True))
            for module_id in cli_ctx.authorized_modules:
                module_info = registry.get_module(module_id)
                if module_info:
                    click.echo(f"  {module_id:<15} {module_info.description}")
            click.echo()
        
        click.echo("使用 --help 查看详细帮助信息")
    
    def register_module_command(self, module_info: ModuleInfo):
        """注册模块命令"""
        handler = module_info.handler

        # 检查handler是否已经是Click命令
        if hasattr(handler, 'callback') and hasattr(handler, 'params'):
            # 如果已经是Click命令，直接克隆并添加安全检查
            import copy

            # 克隆原始命令
            cloned_command = copy.deepcopy(handler)

            # 更新命令名称和帮助信息
            cloned_command.name = module_info.id
            if not cloned_command.help:
                cloned_command.help = module_info.description

            # 保存原始回调函数
            original_callback = cloned_command.callback

            # 创建包装的回调函数
            def security_wrapped_callback(*args, **kwargs):
                # 获取当前上下文
                ctx = click.get_current_context()
                cli_ctx = ctx.obj

                # 执行许可证验证
                if cli_ctx.license_status != LicenseStatus.VALID:
                    status_msg = PermissionManager.get_status_message()
                    click.echo(click.style(f"错误: {status_msg}", fg='red'), err=True)
                    ctx.exit(1)

                # 执行模块授权验证
                if not cli_ctx.is_module_authorized(module_info.id):
                    error_msg = f"模块 '{module_info.id}' 未被授权\n当前授权模块: {', '.join(cli_ctx.authorized_modules)}"
                    click.echo(click.style(error_msg, fg='red'), err=True)
                    ctx.exit(5)

                # 执行原始命令
                try:
                    result = original_callback(*args, **kwargs)
                    return result
                except CLIError as e:
                    click.echo(click.style(f"错误: {e.message}", fg='red'), err=True)
                    ctx.exit(e.error_code)
                except Exception as e:
                    click.echo(click.style(f"未知错误: {str(e)}", fg='red'), err=True)
                    ctx.exit(99)

            # 替换回调函数
            cloned_command.callback = security_wrapped_callback

            # 添加到主组
            self.main_group.add_command(cloned_command)

        else:
            # 如果不是Click命令，使用原来的方式创建包装器
            self._register_legacy_command(module_info)

    def _register_legacy_command(self, module_info: ModuleInfo):
        """注册传统（非Click）命令"""
        handler = module_info.handler

        @click.command(name=module_info.id, help=module_info.description)
        @require_valid_license
        @require_module_auth(module_info.id)
        @click.pass_context
        def command_func(ctx, **kwargs):
            try:
                result = handler(**kwargs)
                if result is not None:
                    click.echo(click.style("命令执行完成", fg='green'))
            except CLIError as e:
                click.echo(click.style(f"错误: {e.message}", fg='red'), err=True)
                ctx.exit(e.error_code)
            except Exception as e:
                click.echo(click.style(f"未知错误: {str(e)}", fg='red'), err=True)
                ctx.exit(99)

        # 添加命令到主组
        self.main_group.add_command(command_func)
    
    def register_all_authorized_modules(self):
        """注册所有授权的模块命令"""
        for module_id in self.cli_context.authorized_modules:
            module_info = registry.get_module(module_id)
            if module_info:
                self.register_module_command(module_info)
    
    def add_license_info_command(self):
        """添加许可证信息命令"""
        @self.main_group.command(name='license-info', help='显示详细许可证信息')
        @click.pass_context
        def license_info(ctx):
            status_msg = PermissionManager.get_status_message()
            click.echo(status_msg)
    
    def run(self, args=None):
        """运行CLI应用"""
        try:
            # 注册授权的模块命令
            self.register_all_authorized_modules()
            # 添加许可证信息命令
            self.add_license_info_command()
            # 运行主命令组
            self.main_group(args)
        except click.ClickException as e:
            e.show()
            sys.exit(e.exit_code)
        except Exception as e:
            click.echo(click.style(f"应用程序错误: {str(e)}", fg='red'), err=True)
            sys.exit(1)

# 创建全局CLI实例
cli_app = DynamicCLI()
