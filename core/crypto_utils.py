import base64
import hashlib
import hmac
import json
from cryptography.fernet import <PERSON><PERSON><PERSON>
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
from typing import Dict, Any
import os

class CryptoManager:
    def __init__(self, master_key: str = None):
        """初始化加密管理器"""
        # 使用固定的盐值和主密钥（实际部署时应更安全地管理）
        self.salt = b'offline_cli_salt_2024'
        self.master_key = master_key or "OfflineCLI_MasterKey_2024"
        self._fernet = None
    
    def _get_fernet(self) -> Fernet:
        """获取Fernet加密实例"""
        if self._fernet is None:
            # 从主密钥派生加密密钥
            kdf = PBKDF2HMAC(
                algorithm=hashes.SHA256(),
                length=32,
                salt=self.salt,
                iterations=100000,
            )
            key = base64.urlsafe_b64encode(kdf.derive(self.master_key.encode()))
            self._fernet = Fernet(key)
        return self._fernet
    
    def encrypt_data(self, data: Dict[str, Any]) -> str:
        """加密数据"""
        json_data = json.dumps(data, ensure_ascii=False)
        encrypted = self._get_fernet().encrypt(json_data.encode('utf-8'))
        return base64.urlsafe_b64encode(encrypted).decode('utf-8')
    
    def decrypt_data(self, encrypted_data: str) -> Dict[str, Any]:
        """解密数据"""
        try:
            encrypted_bytes = base64.urlsafe_b64decode(encrypted_data.encode('utf-8'))
            decrypted = self._get_fernet().decrypt(encrypted_bytes)
            return json.loads(decrypted.decode('utf-8'))
        except Exception as e:
            raise ValueError(f"许可证解密失败: {e}")
    
    def generate_signature(self, data: Dict[str, Any]) -> str:
        """生成数据签名"""
        json_data = json.dumps(data, sort_keys=True, ensure_ascii=False)
        signature = hmac.new(
            self.master_key.encode('utf-8'),
            json_data.encode('utf-8'),
            hashlib.sha256
        ).hexdigest()
        return signature
    
    def verify_signature(self, data: Dict[str, Any], signature: str) -> bool:
        """验证数据签名"""
        expected_signature = self.generate_signature(data)
        return hmac.compare_digest(expected_signature, signature)

# 全局加密管理器实例
crypto_manager = CryptoManager()