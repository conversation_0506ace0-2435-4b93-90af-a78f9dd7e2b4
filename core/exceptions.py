"""
自定义异常类
提供更精确的错误处理和用户友好的错误信息
"""

class CLIError(Exception):
    """CLI应用程序基础异常类"""
    
    def __init__(self, message: str, error_code: int = 1):
        self.message = message
        self.error_code = error_code
        super().__init__(self.message)

class LicenseError(CLIError):
    """许可证相关异常"""
    pass

class LicenseNotFoundError(LicenseError):
    """许可证文件未找到"""
    
    def __init__(self, license_path: str):
        message = f"许可证文件未找到: {license_path}\n请联系管理员获取许可证文件"
        super().__init__(message, error_code=2)

class LicenseExpiredError(LicenseError):
    """许可证已过期"""
    
    def __init__(self, end_date: str):
        message = f"许可证已于 {end_date} 过期\n请联系管理员更新许可证"
        super().__init__(message, error_code=3)

class LicenseCorruptedError(LicenseError):
    """许可证文件损坏"""
    
    def __init__(self, details: str = ""):
        message = "许可证文件损坏或被篡改"
        if details:
            message += f"\n详细信息: {details}"
        message += "\n请联系管理员重新获取有效的许可证文件"
        super().__init__(message, error_code=4)

class ModuleNotAuthorizedError(LicenseError):
    """模块未授权"""
    
    def __init__(self, module_id: str, authorized_modules: list):
        message = (f"模块 '{module_id}' 未被授权\n"
                  f"当前授权模块: {', '.join(authorized_modules) if authorized_modules else '无'}")
        super().__init__(message, error_code=5)

class ExecutionLimitExceededError(LicenseError):
    """执行次数限制已达到"""
    
    def __init__(self, max_executions: int):
        message = f"已达到最大执行次数限制 ({max_executions})\n请联系管理员获取新的许可证"
        super().__init__(message, error_code=6)

class ModuleError(CLIError):
    """模块相关异常"""
    pass

class ModuleNotFoundError(ModuleError):
    """模块未找到"""
    
    def __init__(self, module_id: str):
        message = f"模块 '{module_id}' 未找到或未注册"
        super().__init__(message, error_code=10)

class ModuleDependencyError(ModuleError):
    """模块依赖错误"""
    
    def __init__(self, module_id: str, missing_deps: list):
        message = (f"模块 '{module_id}' 缺少依赖: {', '.join(missing_deps)}")
        super().__init__(message, error_code=11)

class FileProcessingError(CLIError):
    """文件处理异常"""
    pass

class UnsupportedFileFormatError(FileProcessingError):
    """不支持的文件格式"""
    
    def __init__(self, file_path: str, supported_formats: list):
        message = (f"不支持的文件格式: {file_path}\n"
                  f"支持的格式: {', '.join(supported_formats)}")
        super().__init__(message, error_code=20)

class FilePathNotFoundError(FileProcessingError):
    """文件未找到"""
    
    def __init__(self, file_path: str):
        message = f"文件/路径不存在: {file_path}"
        super().__init__(message, error_code=21)

class FilePermissionError(FileProcessingError):
    """文件权限错误"""
    
    def __init__(self, file_path: str, operation: str):
        message = f"文件权限不足，无法{operation}: {file_path}"
        super().__init__(message, error_code=22)

class ValidationError(CLIError):
    """输入验证异常"""
    
    def __init__(self, field: str, value: str, reason: str):
        message = f"输入验证失败 - {field}: '{value}'\n原因: {reason}"
        super().__init__(message, error_code=30)

class ConfigurationError(CLIError):
    """配置错误"""
    
    def __init__(self, config_item: str, details: str = ""):
        message = f"配置错误: {config_item}"
        if details:
            message += f"\n详细信息: {details}"
        super().__init__(message, error_code=40)

def handle_exception(func):
    """异常处理装饰器"""
    def wrapper(*args, **kwargs):
        try:
            return func(*args, **kwargs)
        except CLIError:
            # 重新抛出CLI异常，让上层处理
            raise
        except FileNotFoundError as e:
            raise FileNotFoundError(str(e))
        except PermissionError as e:
            raise FilePermissionError(str(e), "访问")
        except ValueError as e:
            raise ValidationError("输入值", str(e), "格式不正确")
        except Exception as e:
            # 将未知异常包装为CLI异常
            raise CLIError(f"未知错误: {str(e)}", error_code=99)
    
    return wrapper
