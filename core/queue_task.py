
import asyncio
import uuid
import time
from core.log import setup_logger
from typing import Dict, Any, Optional, Callable, List, Union
from datetime import datetime, timedelta
from dataclasses import dataclass, field
from enum import Enum
from concurrent.futures import ThreadPoolExecutor
import threading

# 尝试导入 rich 库用于进度条显示
try:
    from rich.console import Console
    from rich.progress import (
        Progress, TaskID, SpinnerColumn, TextColumn,
        BarColumn, MofNCompleteColumn, TimeElapsedColumn,
        TimeRemainingColumn, TransferSpeedColumn
    )
    from rich.table import Table
    from rich.panel import Panel
    from rich.text import Text
    RICH_AVAILABLE = True
except ImportError:
    RICH_AVAILABLE = False
    print("警告: rich库未安装，将使用基础进度显示。建议安装: pip install rich")

logger = setup_logger(__name__)

class TaskStatus(Enum):
    """任务状态枚举"""
    PENDING = "pending"
    RUNNING = "running"
    SUCCESS = "success"
    FAILED = "failed"
    RETRYING = "retrying"
    CANCELLED = "cancelled"

@dataclass
class QueueConfig:
    """队列配置类"""
    max_workers: int = 5
    max_retries: int = 3
    retry_delay: float = 1.0
    timeout: Optional[float] = None
    progress_description: str = "处理任务"
    show_progress: bool = True
    show_detailed_progress: bool = True
    progress_update_interval: float = 0.1
    enable_statistics: bool = True

@dataclass
class TaskResult:
    """任务结果类"""
    task_id: str
    status: TaskStatus
    result: Any = None
    error: Optional[Exception] = None
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    retry_count: int = 0
    execution_time: Optional[float] = None

    def __post_init__(self):
        if self.start_time and self.end_time:
            self.execution_time = (self.end_time - self.start_time).total_seconds()

@dataclass
class TaskItem:
    """任务项，支持优先级排序"""
    task_id: str
    priority: int
    created_at: datetime
    task_data: Any
    callback: Optional[Callable] = None
    max_retries: Optional[int] = None
    timeout: Optional[float] = None

    def __post_init__(self):
        if not self.task_id:
            self.task_id = str(uuid.uuid4())

    def __lt__(self, other):
        if self.priority != other.priority:
            return self.priority > other.priority
        return self.created_at < other.created_at

@dataclass
class QueueStatistics:
    """队列统计信息"""
    total_tasks: int = 0
    completed_tasks: int = 0
    failed_tasks: int = 0
    cancelled_tasks: int = 0
    total_execution_time: float = 0.0
    average_execution_time: float = 0.0
    tasks_per_second: float = 0.0
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None

    def update_completion(self, execution_time: float):
        """更新完成统计"""
        self.completed_tasks += 1
        self.total_execution_time += execution_time
        if self.completed_tasks > 0:
            self.average_execution_time = self.total_execution_time / self.completed_tasks

        if self.start_time:
            elapsed = (datetime.now() - self.start_time).total_seconds()
            if elapsed > 0:
                self.tasks_per_second = self.completed_tasks / elapsed

class AsyncTaskQueue:
    """异步任务队列"""
    def __init__(self, config: Optional[QueueConfig] = None):
        self.config = config or QueueConfig()
        self._queue = asyncio.PriorityQueue()
        self._results: Dict[str, TaskResult] = {}
        self._running_tasks: Dict[str, asyncio.Task] = {}
        self._statistics = QueueStatistics()
        self._console = Console() if RICH_AVAILABLE else None
        self._progress = None
        self._progress_task = None
        self._lock = threading.Lock()
        self._shutdown = False

        # 线程池用于执行同步任务
        self._thread_pool = ThreadPoolExecutor(max_workers=self.config.max_workers)

        logger.info(f"初始化异步任务队列，最大工作线程数: {self.config.max_workers}")

    async def add_task(self,
                      task_func: Callable,
                      task_data: Any,
                      priority: int = 0,
                      task_id: Optional[str] = None,
                      callback: Optional[Callable] = None,
                      max_retries: Optional[int] = None,
                      timeout: Optional[float] = None) -> str:
        """添加任务到队列"""
        if task_id is None:
            task_id = str(uuid.uuid4())

        task_item = TaskItem(
            task_id=task_id,
            priority=priority,
            created_at=datetime.now(),
            task_data=task_data,
            callback=callback,
            max_retries=max_retries if max_retries is not None else self.config.max_retries,
            timeout=timeout if timeout is not None else self.config.timeout
        )

        # 创建任务结果对象
        self._results[task_id] = TaskResult(
            task_id=task_id,
            status=TaskStatus.PENDING
        )

        await self._queue.put((task_item, task_func))

        logger.debug(f"添加任务到队列: {task_id}, 优先级: {priority}")
        return task_id

    async def process_tasks(self, task_func: Callable, task_data_list: List[Any]) -> List[TaskResult]:
        """批量处理任务"""
        if not task_data_list:
            return []

        self._statistics.start_time = datetime.now()
        self._statistics.total_tasks = len(task_data_list)

        # 启动进度条（在添加任务之前）
        if self.config.show_progress and RICH_AVAILABLE:
            await self._start_progress_display(len(task_data_list))

        # 添加所有任务到队列
        task_ids = []
        for i, data in enumerate(task_data_list):
            task_id = await self.add_task(task_func, data, priority=data.get("priority", 0))
            task_ids.append(task_id)

        # 启动工作线程
        workers = [
            asyncio.create_task(self._worker(f"worker-{i}"))
            for i in range(self.config.max_workers)
        ]

        try:
            # 等待所有任务完成
            await self._wait_for_completion(task_ids)

        finally:
            # 停止工作线程
            self._shutdown = True
            for worker in workers:
                worker.cancel()

            # 等待工作线程结束
            await asyncio.gather(*workers, return_exceptions=True)

            # 停止进度条前确保显示完成状态
            if self._progress and self._progress_task is not None:
                try:
                    # 最后一次更新，确保显示100%
                    completed = sum(1 for result in self._results.values()
                                  if result.status in [TaskStatus.SUCCESS, TaskStatus.FAILED, TaskStatus.CANCELLED])
                    self._progress.update(self._progress_task, completed=completed)
                    logger.debug(f"进度条关闭前最终更新: 完成={completed}")
                except Exception as e:
                    logger.error(f"进度条最终更新失败: {e}")

                self._progress.stop()

            self._statistics.end_time = datetime.now()

        # 返回结果
        return [self._results[task_id] for task_id in task_ids]

    async def _worker(self, worker_name: str):
        """工作线程"""
        logger.debug(f"启动工作线程: {worker_name}")

        while not self._shutdown:
            try:
                # 获取任务（带超时）
                try:
                    task_item, task_func = await asyncio.wait_for(
                        self._queue.get(), timeout=1.0
                    )
                except asyncio.TimeoutError:
                    continue

                # 执行任务
                await self._execute_task(task_item, task_func, worker_name)

            except asyncio.CancelledError:
                logger.debug(f"工作线程 {worker_name} 被取消")
                break
            except Exception as e:
                logger.error(f"工作线程 {worker_name} 发生错误: {e}")

        logger.debug(f"工作线程 {worker_name} 结束")

    async def _execute_task(self, task_item: TaskItem, task_func: Callable, worker_name: str):
        """执行单个任务"""
        task_id = task_item.task_id
        result = self._results[task_id]

        logger.debug(f"工作线程 {worker_name} 开始执行任务: {task_id}")

        result.status = TaskStatus.RUNNING
        result.start_time = datetime.now()

        # 立即更新进度条，显示任务开始执行
        await self._update_progress_immediately()

        try:
            # 执行任务（支持同步和异步函数）
            if asyncio.iscoroutinefunction(task_func):
                if task_item.timeout:
                    task_result = await asyncio.wait_for(
                        task_func(task_item.task_data),
                        timeout=task_item.timeout
                    )
                else:
                    task_result = await task_func(task_item.task_data)
            else:
                # 在线程池中执行同步函数
                loop = asyncio.get_event_loop()
                if task_item.timeout:
                    task_result = await asyncio.wait_for(
                        loop.run_in_executor(
                            self._thread_pool,
                            task_func,
                            task_item.task_data
                        ),
                        timeout=task_item.timeout
                    )
                else:
                    task_result = await loop.run_in_executor(
                        self._thread_pool,
                        task_func,
                        task_item.task_data
                    )

            # 任务成功完成
            result.status = TaskStatus.SUCCESS
            result.result = task_result
            result.end_time = datetime.now()
            result.execution_time = (result.end_time - result.start_time).total_seconds()

            # 更新统计信息
            self._statistics.update_completion(result.execution_time)

            # 立即更新进度条
            await self._update_progress_immediately()

            # 执行回调
            if task_item.callback:
                try:
                    if asyncio.iscoroutinefunction(task_item.callback):
                        await task_item.callback(result)
                    else:
                        task_item.callback(result)
                except Exception as callback_error:
                    logger.warning(f"任务 {task_id} 回调执行失败: {callback_error}")

            logger.debug(f"任务 {task_id} 执行成功，耗时: {result.execution_time:.2f}秒")

        except Exception as e:
            # 任务执行失败
            result.error = e
            result.end_time = datetime.now()
            result.execution_time = (result.end_time - result.start_time).total_seconds()

            # 检查是否需要重试
            max_retries = task_item.max_retries if task_item.max_retries is not None else self.config.max_retries
            if result.retry_count < max_retries:
                result.retry_count += 1
                result.status = TaskStatus.RETRYING

                # 立即更新进度条，显示重试状态
                await self._update_progress_immediately()

                logger.warning(f"任务 {task_id} 执行失败，准备重试 ({result.retry_count}/{max_retries}): {e}")

                # 延迟后重新加入队列
                await asyncio.sleep(self.config.retry_delay)
                await self._queue.put((task_item, task_func))
            else:
                result.status = TaskStatus.FAILED
                self._statistics.failed_tasks += 1
                # 立即更新进度条（失败也算完成）
                await self._update_progress_immediately()
                logger.error(f"任务 {task_id} 执行失败，已达最大重试次数: {e}")

    async def _update_progress_immediately(self):
        """立即更新进度条"""
        if not self._progress or self._progress_task is None:
            return

        try:
            # 计算各种状态的任务数
            completed = 0
            running = 0
            pending = 0

            for result in self._results.values():
                if result.status in [TaskStatus.SUCCESS, TaskStatus.FAILED, TaskStatus.CANCELLED]:
                    completed += 1
                elif result.status in [TaskStatus.RUNNING, TaskStatus.RETRYING]:
                    running += 1
                elif result.status == TaskStatus.PENDING:
                    pending += 1

            # 立即更新进度条（完成的任务数）
            self._progress.update(self._progress_task, completed=completed)

            # 详细的调试信息
            total = len(self._results)
            logger.debug(f"立即更新进度条: 完成={completed}, 运行中={running}, 待处理={pending}, 总计={total}")

        except Exception as e:
            logger.error(f"立即更新进度条失败: {e}")

    async def _wait_for_completion(self, task_ids: List[str]):
        """等待所有任务完成"""
        last_completed = -1

        while True:
            # 检查所有任务是否完成
            completed = 0
            running = 0
            pending = 0

            for task_id in task_ids:
                result = self._results[task_id]
                if result.status in [TaskStatus.SUCCESS, TaskStatus.FAILED, TaskStatus.CANCELLED]:
                    completed += 1
                elif result.status == TaskStatus.RUNNING:
                    running += 1
                elif result.status in [TaskStatus.PENDING, TaskStatus.RETRYING]:
                    pending += 1

            # 总是更新进度条（不只是在变化时）
            if self._progress and self._progress_task is not None:
                try:
                    self._progress.update(self._progress_task, completed=completed)
                    logger.debug(f"进度条更新: 任务ID={self._progress_task}, 完成={completed}/{len(task_ids)}")
                except Exception as e:
                    logger.error(f"进度条更新失败: {e}")

            # 如果进度有变化，记录日志
            if completed != last_completed:
                # logger.info(f"\n进度变化: {completed}/{len(task_ids)} (运行中: {running}, 待处理: {pending})")
                last_completed = completed

            # 检查是否所有任务都完成
            if completed == len(task_ids):
                logger.debug(f"所有任务完成: {completed}/{len(task_ids)}")
                # 确保进度条显示100%完成
                if self._progress and self._progress_task is not None:
                    try:
                        self._progress.update(self._progress_task, completed=len(task_ids))
                        logger.debug(f"最终进度更新: 完成={len(task_ids)}/{len(task_ids)}")
                    except Exception as e:
                        logger.error(f"最终进度更新失败: {e}")
                break

            await asyncio.sleep(self.config.progress_update_interval)

    async def _start_progress_display(self, total_tasks: int):
        """启动进度条显示"""
        if not RICH_AVAILABLE or not self.config.show_progress:
            return

        self._progress = Progress(
            SpinnerColumn(),
            TextColumn("[bold blue]{task.description}"),
            BarColumn(),
            MofNCompleteColumn(),
            TextColumn("•"),
            TimeElapsedColumn(),
            TextColumn("•"),
            TimeRemainingColumn(),
            TextColumn("•"),
            TransferSpeedColumn(),
            console=self._console,
            transient=False,
            refresh_per_second=10  # 增加刷新频率
        )

        self._progress.start()
        self._progress_task = self._progress.add_task(
            self.config.progress_description,
            total=total_tasks,
            completed=0  # 明确设置初始完成数
        )

        logger.debug(f"启动进度条显示，总任务数: {total_tasks}, 任务ID: {self._progress_task}")

    def get_statistics(self) -> QueueStatistics:
        """获取队列统计信息"""
        return self._statistics

    def get_task_result(self, task_id: str) -> Optional[TaskResult]:
        """获取指定任务的结果"""
        return self._results.get(task_id)

    def get_all_results(self) -> Dict[str, TaskResult]:
        """获取所有任务结果"""
        return self._results.copy()

    def display_summary(self):
        """显示执行摘要"""
        if RICH_AVAILABLE and self._console:
            self._display_rich_summary()
        else:
            self._display_simple_summary()

    def _display_rich_summary(self):
        """使用 Rich 显示详细摘要"""
        stats = self._statistics

        # 创建统计表格
        table = Table(title="任务执行摘要", show_header=True, header_style="bold magenta")
        table.add_column("指标", style="cyan", no_wrap=True)
        table.add_column("数值", style="green")

        # 基本统计
        table.add_row("总任务数", str(stats.total_tasks))
        table.add_row("成功完成", str(stats.completed_tasks))
        table.add_row("执行失败", str(stats.failed_tasks))
        table.add_row("取消任务", str(stats.cancelled_tasks))

        # 成功率
        if stats.total_tasks > 0:
            success_rate = (stats.completed_tasks / stats.total_tasks) * 100
            table.add_row("成功率", f"{success_rate:.1f}%")

        # 时间统计
        if stats.start_time and stats.end_time:
            total_time = (stats.end_time - stats.start_time).total_seconds()
            table.add_row("总耗时", f"{total_time:.2f}秒")

        if stats.average_execution_time > 0:
            table.add_row("平均执行时间", f"{stats.average_execution_time:.2f}秒")

        if stats.tasks_per_second > 0:
            table.add_row("处理速度", f"{stats.tasks_per_second:.2f}任务/秒")

        self._console.print(table)

        # 显示失败任务详情
        failed_results = [r for r in self._results.values() if r.status == TaskStatus.FAILED]
        if failed_results:
            self._console.print("\n[bold red]失败任务详情:[/bold red]")
            for result in failed_results[:5]:  # 只显示前5个失败任务
                error_msg = str(result.error) if result.error else "未知错误"
                self._console.print(f"  • 任务 {result.task_id}: {error_msg}")

            if len(failed_results) > 5:
                self._console.print(f"  ... 还有 {len(failed_results) - 5} 个失败任务")

    def _display_simple_summary(self):
        """显示简单摘要（不使用 Rich）"""
        stats = self._statistics

        print("\n" + "="*50)
        print("任务执行摘要")
        print("="*50)
        print(f"总任务数: {stats.total_tasks}")
        print(f"成功完成: {stats.completed_tasks}")
        print(f"执行失败: {stats.failed_tasks}")
        print(f"取消任务: {stats.cancelled_tasks}")

        if stats.total_tasks > 0:
            success_rate = (stats.completed_tasks / stats.total_tasks) * 100
            print(f"成功率: {success_rate:.1f}%")

        if stats.start_time and stats.end_time:
            total_time = (stats.end_time - stats.start_time).total_seconds()
            print(f"总耗时: {total_time:.2f}秒")

        if stats.average_execution_time > 0:
            print(f"平均执行时间: {stats.average_execution_time:.2f}秒")

        if stats.tasks_per_second > 0:
            print(f"处理速度: {stats.tasks_per_second:.2f}任务/秒")

        # 显示失败任务
        failed_results = [r for r in self._results.values() if r.status == TaskStatus.FAILED]
        if failed_results:
            print(f"\n失败任务数: {len(failed_results)}")
            for result in failed_results[:3]:  # 只显示前3个
                error_msg = str(result.error) if result.error else "未知错误"
                print(f"  - 任务 {result.task_id}: {error_msg}")

        print("="*50)

    async def cancel_task(self, task_id: str) -> bool:
        """取消指定任务"""
        if task_id in self._results:
            result = self._results[task_id]
            if result.status in [TaskStatus.PENDING, TaskStatus.RUNNING]:
                result.status = TaskStatus.CANCELLED
                self._statistics.cancelled_tasks += 1
                # 立即更新进度条
                await self._update_progress_immediately()
                logger.info(f"任务 {task_id} 已取消")
                return True
        return False

    async def cancel_all_tasks(self):
        """取消所有待处理任务"""
        cancelled_count = 0
        for task_id, result in self._results.items():
            if result.status in [TaskStatus.PENDING, TaskStatus.RUNNING]:
                result.status = TaskStatus.CANCELLED
                cancelled_count += 1

        self._statistics.cancelled_tasks += cancelled_count
        self._shutdown = True

        # 立即更新进度条
        if cancelled_count > 0:
            await self._update_progress_immediately()

        logger.info(f"已取消 {cancelled_count} 个任务")

    def __del__(self):
        """析构函数，清理资源"""
        if hasattr(self, '_thread_pool'):
            self._thread_pool.shutdown(wait=False)


def run_async_task(coro):
    """运行异步任务的辅助函数"""
    try:
        loop = asyncio.get_event_loop()
        if loop.is_running():
            # 如果已经在事件循环中，创建新任务
            return asyncio.create_task(coro)
        else:
            # 如果不在事件循环中，运行事件循环
            return loop.run_until_complete(coro)
    except RuntimeError:
        # 如果没有事件循环，创建新的
        return asyncio.run(coro)