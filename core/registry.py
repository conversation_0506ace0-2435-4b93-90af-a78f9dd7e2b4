"""
模块注册器
用于动态注册和管理应用程序模块
"""

from typing import Dict, List, Callable, Optional, Any
from dataclasses import dataclass
import logging

logger = logging.getLogger(__name__)

@dataclass
class ModuleInfo:
    """模块信息数据类"""
    id: str
    name: str
    description: str
    handler: Callable
    version: str = "1.0.0"
    dependencies: List[str] = None
    
    def __post_init__(self):
        if self.dependencies is None:
            self.dependencies = []

class ModuleRegistry:
    """模块注册器类"""
    
    def __init__(self):
        self._modules: Dict[str, ModuleInfo] = {}
        self._initialized = False
        
    def register(self, 
                 module_id: str, 
                 name: str, 
                 description: str, 
                 handler: Callable,
                 version: str = "1.0.0",
                 dependencies: List[str] = None) -> None:
        """
        注册模块
        
        Args:
            module_id: 模块唯一标识
            name: 模块显示名称
            description: 模块描述
            handler: 模块处理函数
            version: 模块版本
            dependencies: 依赖的其他模块列表
        """
        if module_id in self._modules:
            logger.warning(f"模块 '{module_id}' 已存在，将被覆盖")
            
        module_info = ModuleInfo(
            id=module_id,
            name=name,
            description=description,
            handler=handler,
            version=version,
            dependencies=dependencies or []
        )
        
        self._modules[module_id] = module_info
        # logger.info(f"模块 '{module_id}' 注册成功")
        
    def unregister(self, module_id: str) -> bool:
        """
        注销模块
        
        Args:
            module_id: 模块标识
            
        Returns:
            bool: 是否成功注销
        """
        if module_id in self._modules:
            del self._modules[module_id]
            logger.info(f"模块 '{module_id}' 注销成功")
            return True
        return False
        
    def get_module(self, module_id: str) -> Optional[ModuleInfo]:
        """
        获取模块信息
        
        Args:
            module_id: 模块标识
            
        Returns:
            ModuleInfo: 模块信息，如果不存在返回None
        """
        return self._modules.get(module_id)
        
    def list_modules(self) -> List[str]:
        """
        获取所有已注册模块的ID列表
        
        Returns:
            List[str]: 模块ID列表
        """
        return list(self._modules.keys())
        
    def get_all_modules(self) -> Dict[str, ModuleInfo]:
        """
        获取所有模块信息
        
        Returns:
            Dict[str, ModuleInfo]: 所有模块信息
        """
        return self._modules.copy()
        
    def is_registered(self, module_id: str) -> bool:
        """
        检查模块是否已注册
        
        Args:
            module_id: 模块标识
            
        Returns:
            bool: 是否已注册
        """
        return module_id in self._modules
        
    def get_authorized_modules(self, authorized_list: List[str]) -> Dict[str, ModuleInfo]:
        """
        获取授权的模块信息
        
        Args:
            authorized_list: 授权模块ID列表
            
        Returns:
            Dict[str, ModuleInfo]: 授权的模块信息
        """
        return {
            module_id: module_info 
            for module_id, module_info in self._modules.items()
            if module_id in authorized_list
        }
        
    def validate_dependencies(self, module_id: str) -> List[str]:
        """
        验证模块依赖
        
        Args:
            module_id: 模块标识
            
        Returns:
            List[str]: 缺失的依赖列表
        """
        module = self.get_module(module_id)
        if not module:
            return [module_id]
            
        missing_deps = []
        for dep in module.dependencies:
            if not self.is_registered(dep):
                missing_deps.append(dep)
                
        return missing_deps

# 全局注册器实例
registry = ModuleRegistry()
