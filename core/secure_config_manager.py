"""
安全配置管理器
实现配置文件的安全加载、验证和保护机制
"""

import json
import hashlib
import hmac
import os
from pathlib import Path
from typing import Dict, Any, Optional, List
from dataclasses import dataclass
import logging

logger = logging.getLogger(__name__)

@dataclass
class SecurityConstraints:
    """安全约束定义"""
    min_security_score: int = 60
    max_security_score: int = 100
    required_components: List[str] = None
    forbidden_modifications: List[str] = None
    
    def __post_init__(self):
        if self.required_components is None:
            self.required_components = [
                'time_proof_chain',
                'system_fingerprint', 
                'anomaly_detection'
            ]
        if self.forbidden_modifications is None:
            self.forbidden_modifications = [
                'security_levels.high.min_score',
                'time_validation.min_security_score',
                'core_security_components'
            ]

class SecureConfigManager:
    """安全配置管理器"""
    
    def __init__(self, config_dir: str = "config"):
        self.config_dir = Path(config_dir)
        self.config_dir.mkdir(exist_ok=True)
        
        # 配置文件路径
        self.user_config_file = self.config_dir / "user_config.json"
        self.security_policy_file = self.config_dir / ".security_policy"
        # 注意：系统配置现在已硬编码，不再使用外部文件
        
        # 安全约束
        self.constraints = SecurityConstraints()
        
        # 硬编码的核心安全参数（不可修改）
        self.CORE_SECURITY_CONFIG = {
            "version": "2.0",
            "hardcoded": True,
            "description": "硬编码核心安全配置，用户无法修改",

            "core_security_components": {
                "time_proof_chain": {
                    "enabled": True,
                    "mandatory": True,
                    "min_chain_length": 1,
                    "max_chain_length": 100,
                    "cleanup_threshold": 50,
                    "signature_algorithm": "HMAC-SHA256"
                },
                "system_fingerprint": {
                    "enabled": True,
                    "mandatory": True,
                    "min_similarity": 0.7,
                    "update_interval_hours": 24,
                    "components": {
                        "platform_info": {"enabled": True, "weight": 0.2},
                        "cpu_info": {"enabled": True, "weight": 0.2},
                        "memory_info": {"enabled": True, "weight": 0.1},
                        "disk_info": {"enabled": True, "weight": 0.1},
                        "network_interfaces": {"enabled": True, "weight": 0.2},
                        "system_files": {"enabled": True, "weight": 0.2}
                    }
                },
                "anomaly_detection": {
                    "enabled": True,
                    "mandatory": True,
                    "max_anomaly_score": 50,
                    "time_source_max_diff_seconds": 300,
                    "system_uptime_check": True,
                    "boot_time_consistency": True,
                    "time_jump_threshold_seconds": 3600,
                    "weights": {
                        "time_source_diff": 0.4,
                        "time_jump": 0.3,
                        "system_consistency": 0.3
                    }
                }
            },

            "security_enforcement": {
                "min_security_score_absolute": 50,  # 绝对最低分数
                "max_user_configurable_score": 80,  # 用户可配置的最高分数
                "strict_mode_min_score": 70,        # 严格模式最低分数
                "mandatory_checks": [
                    "time_validation",
                    "system_integrity",
                    "proof_chain_validation",
                    "fingerprint_verification"
                ],
                "security_levels": {
                    "high": {
                        "min_score": 80,
                        "description": "高安全等级，适用于关键环境",
                        "requirements": {
                            "multiple_time_sources": True,
                            "system_fingerprint_match": True,
                            "time_proof_chain_valid": True,
                            "low_anomaly_score": True
                        }
                    },
                    "medium": {
                        "min_score": 60,
                        "description": "中等安全等级，适用于一般商用环境",
                        "requirements": {
                            "basic_time_validation": True,
                            "system_integrity_check": True,
                            "moderate_anomaly_tolerance": True
                        }
                    },
                    "low": {
                        "min_score": 40,
                        "description": "低安全等级，适用于开发测试环境",
                        "requirements": {
                            "basic_functionality": True
                        }
                    }
                }
            },

            "time_validation": {
                "min_security_score": 60,           # 默认最低安全分数
                "max_time_jump_seconds": 3600,      # 最大时间跳跃
                "require_high_confidence": False,   # 是否要求高置信度
                "time_sources": {
                    "system_time": {"enabled": True, "weight": 0.3},
                    "hardware_clock": {"enabled": True, "weight": 0.4},
                    "filesystem_time": {"enabled": True, "weight": 0.2},
                    "process_time": {"enabled": True, "weight": 0.1}
                }
            },

            "risk_thresholds": {
                "time_tampering": {
                    "low_risk": 10,
                    "medium_risk": 30,
                    "high_risk": 50,
                    "critical_risk": 70
                },
                "system_compromise": {
                    "fingerprint_similarity": 0.7,
                    "anomaly_score": 40,
                    "proof_chain_breaks": 3
                }
            }
        }
        
        # 初始化配置
        self._initialize_secure_config()
    
    def _initialize_secure_config(self):
        """初始化安全配置"""
        # 创建安全策略文件（如果不存在）
        if not self.security_policy_file.exists():
            self._create_security_policy()

        # 系统配置现在已硬编码，无需创建外部文件
        logger.info("核心安全配置已硬编码，无法被外部修改")
    

    
    def _create_security_policy(self):
        """创建安全策略文件"""
        policy = {
            "policy_version": "1.0",
            "enforcement_level": "strict",
            "allowed_user_modifications": [
                "logging.log_level",
                "logging.enabled",
                "logging.events_to_log.*",
                "alerts.enabled",
                "alerts.alert_conditions.*",
                "maintenance.auto_cleanup.cleanup_interval_hours",
                "maintenance.auto_cleanup.keep_logs_days",
                "maintenance.auto_cleanup.keep_proofs_days",
                "user_interface.*",
                "performance.cache_system_fingerprint",
                "performance.fingerprint_cache_duration_minutes",
                "performance.enable_fast_mode",
                "compatibility.fallback_mode.enabled",
                "compatibility.legacy_support.*"
            ],
            "protected_parameters": [
                "core_security_components.*",
                "security_enforcement.*",
                "time_validation.min_security_score",
                "security_levels.*.min_score"
            ],
            "parameter_constraints": {
                "time_validation.max_time_jump_seconds": {
                    "min": 300,   # 5分钟
                    "max": 7200   # 2小时
                },
                "anomaly_detection.time_source_max_diff_seconds": {
                    "min": 60,    # 1分钟
                    "max": 1800   # 30分钟
                }
            }
        }
        
        self._save_protected_config(self.security_policy_file, policy)
        logger.info("安全策略文件创建成功")
    
    def load_secure_config(self) -> Dict[str, Any]:
        """加载安全配置"""
        try:
            # 1. 使用硬编码的系统配置（核心安全参数）
            system_config = self.CORE_SECURITY_CONFIG.copy()
            logger.info("使用硬编码的核心安全配置")

            # 2. 加载用户配置（可选）
            user_config = {}
            if self.user_config_file.exists():
                try:
                    user_config = self._load_user_config()
                    if user_config:
                        # 验证用户配置的安全性
                        user_config = self._validate_user_config(user_config)
                        logger.info("用户配置加载并验证成功")
                except Exception as e:
                    logger.warning(f"用户配置加载失败，使用默认配置: {e}")
                    user_config = {}

            # 3. 合并配置（系统配置优先）
            merged_config = self._merge_configs(system_config, user_config)

            # 4. 应用安全约束
            final_config = self._apply_security_constraints(merged_config)

            logger.info("安全配置加载成功")
            return final_config

        except Exception as e:
            logger.error(f"配置加载失败: {e}")
            # 返回最小安全配置
            return self._get_minimal_secure_config()

    def _load_user_config(self) -> Dict[str, Any]:
        """加载用户配置文件（简化版，不需要签名验证）"""
        try:
            with open(self.user_config_file, 'r', encoding='utf-8') as f:
                content = f.read().strip()

            # 移除注释行
            lines = []
            for line in content.split('\n'):
                line = line.strip()
                if line and not line.startswith('_comment') and not line.startswith('_warning'):
                    lines.append(line)

            clean_content = '\n'.join(lines)
            return json.loads(clean_content)

        except Exception as e:
            logger.error(f"用户配置文件加载失败: {e}")
            return {}
    
    def _validate_user_config(self, user_config: Dict[str, Any]) -> Dict[str, Any]:
        """验证用户配置的安全性"""
        # 加载安全策略
        policy = self._load_protected_config(self.security_policy_file)
        if not policy:
            logger.warning("安全策略文件缺失，使用默认策略")
            return {}
        
        validated_config = {}
        allowed_modifications = policy.get("allowed_user_modifications", [])
        parameter_constraints = policy.get("parameter_constraints", {})
        
        # 递归验证配置参数
        def validate_parameter(key_path: str, value: Any) -> bool:
            # 检查是否允许修改
            if not any(self._match_pattern(key_path, pattern) for pattern in allowed_modifications):
                logger.warning(f"用户配置参数被拒绝: {key_path}")
                return False
            
            # 检查参数约束
            if key_path in parameter_constraints:
                constraint = parameter_constraints[key_path]
                if isinstance(value, (int, float)):
                    if "min" in constraint and value < constraint["min"]:
                        logger.warning(f"参数值过小: {key_path}={value}")
                        return False
                    if "max" in constraint and value > constraint["max"]:
                        logger.warning(f"参数值过大: {key_path}={value}")
                        return False
            
            return True
        
        # 扁平化配置并验证
        flat_config = self._flatten_dict(user_config)
        for key_path, value in flat_config.items():
            if validate_parameter(key_path, value):
                self._set_nested_value(validated_config, key_path, value)
        
        return validated_config
    
    def _merge_configs(self, system_config: Dict[str, Any], 
                      user_config: Dict[str, Any]) -> Dict[str, Any]:
        """合并系统配置和用户配置"""
        merged = system_config.copy()
        
        # 递归合并，但系统配置优先
        def merge_recursive(target: Dict, source: Dict):
            for key, value in source.items():
                if key in target:
                    if isinstance(target[key], dict) and isinstance(value, dict):
                        merge_recursive(target[key], value)
                    # 系统配置优先，不覆盖
                else:
                    target[key] = value
        
        merge_recursive(merged, user_config)
        return merged
    
    def _apply_security_constraints(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """应用安全约束"""
        # 确保核心安全组件启用
        core_components = config.get("core_security_components", {})
        for component, settings in core_components.items():
            if settings.get("mandatory", False):
                settings["enabled"] = True
        
        # 确保最低安全分数
        min_score = config.get("security_enforcement", {}).get("min_security_score_absolute", 50)
        
        # 检查并修正时间验证配置
        time_validation = config.get("time_validation", {})
        if time_validation.get("min_security_score", 0) < min_score:
            time_validation["min_security_score"] = min_score
            logger.warning(f"安全分数已调整到最低要求: {min_score}")
        
        # 检查并修正安全等级配置
        security_levels = config.get("security_levels", {})
        for level, settings in security_levels.items():
            if settings.get("min_score", 0) < min_score:
                settings["min_score"] = min_score
        
        return config
    
    def _get_minimal_secure_config(self) -> Dict[str, Any]:
        """获取最小安全配置（故障安全）"""
        # 直接返回硬编码的核心配置，确保最高安全性
        minimal_config = self.CORE_SECURITY_CONFIG.copy()

        # 添加最严格的安全设置
        minimal_config.update({
            "offline_security": {
                "enabled": True,
                "strict_mode": True,
                "description": "故障安全模式 - 最高安全等级"
            }
        })

        # 强制最高安全分数
        minimal_config["time_validation"]["min_security_score"] = 80
        minimal_config["time_validation"]["require_high_confidence"] = True

        logger.warning("使用最小安全配置（故障安全模式）")
        return minimal_config
    
    def _save_protected_config(self, file_path: Path, config: Dict[str, Any]):
        """保存受保护的配置文件"""
        try:
            # 序列化配置
            config_json = json.dumps(config, indent=2, ensure_ascii=False)
            
            # 生成完整性哈希
            config_hash = hashlib.sha256(config_json.encode()).hexdigest()
            
            # 生成HMAC签名
            signing_key = self._get_config_signing_key()
            signature = hmac.new(
                signing_key,
                config_json.encode(),
                hashlib.sha256
            ).hexdigest()
            
            # 保存配置文件
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(config_json)
                f.write(f"\n# INTEGRITY_HASH: {config_hash}")
                f.write(f"\n# SIGNATURE: {signature}")
            
            # 设置文件权限（只读）
            if os.name != 'nt':  # 非Windows系统
                os.chmod(file_path, 0o444)
                
        except Exception as e:
            logger.error(f"保存配置文件失败: {e}")
            raise
    
    def _load_protected_config(self, file_path: Path) -> Optional[Dict[str, Any]]:
        """加载受保护的配置文件"""
        try:
            if not file_path.exists():
                return None
            
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            lines = content.strip().split('\n')
            
            # 分离配置内容和验证信息
            config_lines = []
            integrity_hash = None
            signature = None
            
            for line in lines:
                if line.startswith('# INTEGRITY_HASH:'):
                    integrity_hash = line.split(':', 1)[1].strip()
                elif line.startswith('# SIGNATURE:'):
                    signature = line.split(':', 1)[1].strip()
                else:
                    config_lines.append(line)
            
            config_json = '\n'.join(config_lines)
            
            # 验证完整性
            if integrity_hash:
                calculated_hash = hashlib.sha256(config_json.encode()).hexdigest()
                if calculated_hash != integrity_hash:
                    logger.error(f"配置文件完整性验证失败: {file_path}")
                    return None
            
            # 验证签名
            if signature:
                signing_key = self._get_config_signing_key()
                expected_signature = hmac.new(
                    signing_key,
                    config_json.encode(),
                    hashlib.sha256
                ).hexdigest()
                
                if not hmac.compare_digest(signature, expected_signature):
                    logger.error(f"配置文件签名验证失败: {file_path}")
                    return None
            
            # 解析配置
            return json.loads(config_json)
            
        except Exception as e:
            logger.error(f"加载配置文件失败: {e}")
            return None
    
    def _get_config_signing_key(self) -> bytes:
        """获取配置文件签名密钥"""
        # 基于系统特征生成一致的密钥
        try:
            import psutil
            system_info = {
                'platform': os.name,
                'cpu_count': psutil.cpu_count(),
                'boot_time': int(psutil.boot_time())
            }
            key_material = json.dumps(system_info, sort_keys=True)
            return hashlib.sha256(key_material.encode()).digest()
        except:
            # 降级到固定密钥（不够安全，但保证功能）
            return b"offline_license_config_key_2024"
    
    def _match_pattern(self, key_path: str, pattern: str) -> bool:
        """匹配配置路径模式"""
        if pattern.endswith('*'):
            return key_path.startswith(pattern[:-1])
        return key_path == pattern
    
    def _flatten_dict(self, d: Dict[str, Any], parent_key: str = '') -> Dict[str, Any]:
        """扁平化字典"""
        items = []
        for k, v in d.items():
            new_key = f"{parent_key}.{k}" if parent_key else k
            if isinstance(v, dict):
                items.extend(self._flatten_dict(v, new_key).items())
            else:
                items.append((new_key, v))
        return dict(items)
    
    def _set_nested_value(self, d: Dict[str, Any], key_path: str, value: Any):
        """设置嵌套字典值"""
        keys = key_path.split('.')
        current = d
        for key in keys[:-1]:
            if key not in current:
                current[key] = {}
            current = current[key]
        current[keys[-1]] = value

# 全局安全配置管理器实例
secure_config_manager = SecureConfigManager()
