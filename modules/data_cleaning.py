import asyncio
import json
import os
from pathlib import Path

import click
from typing import Optional, Dict, Any

from core.queue_task import QueueConfig, AsyncTaskQueue, run_async_task
from core.registry import registry
from core.exceptions import *
from core.setting import tool_settings
from core.log import setup_logger
from modules.cleaning import DataCleaner
from utils.encrypted import is_file_encrypted, calculate_file_hash
from utils.file import copy_file, extract_zip_rar_7z, get_document_metadata
from utils.mail import parse_eml

logger = setup_logger(__name__)

file_info = []
@click.command()
@click.argument('input_path', type=click.Path( readable=True))
@click.option('-o', '--output', 'output_path',
              type=click.Path(writable=True),
              help='输出文件路径')
@click.option('--ocr', 'ocr', is_flag=True, help='是否对PDF进行OCR, 默认跳过包含文本的页面')
@click.option('--force_ocr', 'force_ocr', is_flag=True, help='强制对每一页进行OCR，包括有文本的页面')
@handle_exception
def clean_data(input_path: str, output_path: Optional[str], ocr: bool = False, force_ocr: bool = False):
    """数据清理功能 - 重命名文档，提取邮件中的正文和附件，转换doc为docx, 对pdf进行ocr"""
    if not output_path:
        raise ValueError("输出路径不能为空")

        # 初始化清理器
    cleaner = DataCleaner(input_path, output_path, ocr, force_ocr)
    cleaner.process()
    click.echo("数据清理完成")
    # 确保输出目录存在
    # os.makedirs(output_path, exist_ok=True)
    # # 判断material_path是否存在
    # if not os.path.exists(input_path):
    #     raise FilePathNotFoundError(input_path)
    #
    # password_detected_path = os.path.join(output_path, "encrypted_document")
    # error_files_path = os.path.join(output_path, "error_files")
    # result_path = os.path.join(output_path, "result")
    # for path in [password_detected_path, error_files_path, result_path]:
    #     os.makedirs(path, exist_ok=True)
    #
    # task_data = []
    # def append_task_data(filepath: str):
    #     task_data.append({
    #         'file_path': filepath,
    #         'password_detected_path': password_detected_path,
    #         'error_files_path': error_files_path,
    #         'result_path': result_path
    #     })
    # # 如果file_path是文件而不是目录，则直接处理该文件
    # if os.path.isfile(input_path):
    #     append_task_data(input_path)
    # else:
    #     ext_blacklist = [".meta"]
    #     for root, dirs, files in os.walk(input_path):
    #         for file in files:
    #             # 检查文件后缀是否在黑名单中
    #             if file.endswith(tuple(ext_blacklist)):
    #                 continue
    #                 # 检查文件是否可访问
    #             if not file.endswith(tuple(['.rar', '.zip', '.7z'])):
    #                 logger.warning(f"不支持此压缩包类型: {file}")
    #                 continue
    #             file_path = os.path.join(root, file)
    #             if not os.access(file_path, os.R_OK):
    #                 logger.error(f"无法访问文件: {file_path}")
    #                 continue
    #             if file.endswith(".zip") or file.endswith(".rar") or file.endswith(".7z"):
    #                 # 判断压缩包存不存在密码
    #                 if is_file_encrypted(os.path.join(root, file)):
    #                     # 移动到密码检测目录
    #                     copy_file(os.path.join(root, file), password_detected_path)
    #                     logger.warning(f"检测到加密文件: {file_path}")
    #                     continue
    #                 # 解压压缩包
    #                 logger.info(f"正在解压文件: {file_path}")
    #                 zip_name, _ = os.path.splitext(os.path.basename(file))
    #                 unpack_files = extract_zip_rar_7z(os.path.join(root, file),
    #                                                   os.path.join(input_path, zip_name))
    #                 # 遍历解压后的文件
    #                 for unpack_file in unpack_files:
    #                     # 检查文件是否可访问
    #                     if not os.access(unpack_file, os.R_OK):
    #                         logger.warning(f"无法访问文件: {unpack_file}")
    #                         continue
    #
    #                     append_task_data(str(unpack_file))
    #
    #                 continue
    #             append_task_data(str(file_path))
    #
    #     # 配置队列
    # config = QueueConfig(
    #     max_workers=tool_settings.tool_config.limits,
    #     max_retries=2,
    #     progress_description="数据清洗任务",
    #     show_progress=True,
    #     show_detailed_progress=True
    # )
    #
    # # 创建队列并处理任务
    # queue = AsyncTaskQueue(config)
    # async def run_test():
    #     return await queue.process_tasks(rename_file, task_data)
    #
    # run_async_task(run_test())
    #
    # # 显示结果摘要
    # queue.display_summary()
    # json_file_path = os.path.join(output_path, "file_records.json")
    # try:
    #     with open(json_file_path, 'w', encoding='utf-8') as f:
    #         json.dump(file_info, f, ensure_ascii=False, indent=4)
    # except Exception as e:
    #     logger.error(f"保存文件记录失败: {str(e)}")
    # click.echo("数据清理完成")

file_counter = 1
async def rename_file(task_data: Dict[str, str]):
    """重命名文件, 检测密码， 提取邮件中的附件"""
    file_path = task_data['file_path']
    password_detected_path = task_data['password_detected_path']
    error_files_path = task_data['error_files_path']
    result_path = task_data['result_path']
    file_record = {}
    await asyncio.sleep(0.1)
    try:
        global file_counter
        file_ext = os.path.splitext(file_path)[1] or ''
        file_record['old_file_path'] = file_path
        if file_ext in ['.eml']:
            attachments=[]
            email_save_path = os.path.join(result_path, os.path.splitext(os.path.basename(file_path))[0])
            os.makedirs(email_save_path, exist_ok=True)
            file_record['new_file_path'] = email_save_path
            mail_info = parse_eml(file_path)
            mail_html_file = os.path.join(email_save_path, 'mail.html')
            with open(mail_html_file, 'w', encoding='utf-8') as f:
                f.write(mail_info['mail_html'])
            for attachment in mail_info['attachments']:
                new_name = f"{file_counter:04d}{os.path.splitext(attachment['filename'])[1]}"
                att_info = {
                    'filename': attachment['filename'],
                    'new_filename': new_name,
                    'encrypted': False
                }
                new_path = os.path.join(email_save_path, new_name)
                with open(new_path, 'wb') as f:
                    f.write(attachment['content'])
                if is_file_encrypted(file_path):
                    logger.info(f"文件 [{file_path}] 检测到加密附件，已保存至 [{password_detected_path}]")
                    att_info['encrypted'] = True
                    os.rename(new_path, os.path.join(password_detected_path, new_name))
                attachments.append(att_info)
                file_counter += 1
            file_record['attachments'] = attachments
            file_info.append(file_record)
            file_counter += 1
            return
        # 检测密码
        new_file_name = f"{file_counter:04d}{file_ext}"
        new_file_path = os.path.join(result_path, new_file_name)
        file_record['new_file_path'] = new_file_path
        if is_file_encrypted(file_path):
            new_file_path = os.path.join(password_detected_path, os.path.basename(file_path))
            os.rename(file_path, new_file_path)
            file_info.append(f"检测到加密文件: {new_file_path}")
            return
        # 获取文档元数据
        file_record['metadata'] = get_document_metadata(file_path)
        file_record['hash'] = calculate_file_hash(file_path)
        copy_file(file_path, new_file_path)
        file_counter += 1
        file_info.append(file_record)


    except Exception as e:
        print(e)
        # logger.error(f"处理文件时出错: {file_path}")
        new_file_path = os.path.join(error_files_path, os.path.basename(file_path))
        copy_file(file_path, new_file_path)
# 注册模块
registry.register(
    'data_cleaning',
    '数据清理',
    '重命名文档，提取邮件中的正文和附件，转换doc为docx',
    clean_data
)