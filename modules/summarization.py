import asyncio
import os

import click
import re
from typing import List, Optional, Tuple
from pathlib import Path
from collections import Counter
import math

from core.registry import registry
from core.exceptions import *
from modules.summary import SummaryTool


@click.command()
@click.argument('file_path', type=click.Path(exists=True, readable=True))
@click.option('-o', '--output', 'output_path',
              type=click.Path(writable=True),
              help='输出目录')
@click.option('-k', '--keywords', 'keywords_file',
              type=click.Path(readable=True),
              help='关键词文件路径')
@handle_exception
def summarize_file(file_path: str, output_path: Optional[str], keywords_file: Optional[str]):
    """文本摘要功能 - 从文本文件生成摘要"""
    if not output_path:
        raise ValueError("输出路径不能为空")
    if not os.path.exists(file_path):
        raise FileNotFoundError(f"输入路径不存在: {file_path}")
    # if not os.path.exists(keywords_file):
    #     raise FileNotFoundError(f"关键词文件不存在: {keywords_file}")
    try:
        summary = SummaryTool(file_path, output_path, keywords_file)
        asyncio.run(summary.check_health())
        summary.process()
    except Exception as e:
        raise e




# 注册模块
registry.register(
    'summarization',
    '文本摘要',
    '从文本文件生成智能摘要',
    summarize_file
)