import json
import os.path
import re
import logging
from pathlib import Path
from typing import List, Dict, Any, Callable, Tuple

import fitz
from bs4 import BeautifulSoup
import httpx
import docx
import pandas as pd
from pptx import Presentation
from websockets import SecurityError

from core.queue_task import QueueConfig, AsyncTaskQueue, run_async_task
from core.setting import tool_settings
from openai import OpenAI


class ToolBase:
    """工具基类"""
    def __init__(self, input_path: str, output_path: str, logger: Any, keyword_file: str):
        # 设置httpx日志级别为WARNING，避免输出DEBUG信息
        logging.getLogger("httpx").setLevel(logging.WARNING)
        
        self.openai_client = OpenAI(
            api_key=tool_settings.llm_config.api_key,
            base_url=tool_settings.llm_config.base_url,
        )
        self.llm_config = tool_settings.llm_config
        self.http_client = httpx.AsyncClient(
            timeout=30,
        )
        self.logger = logger
        self.input_path = Path(input_path)
        self.output_path = Path(output_path)
        self.keywords = []
        if keyword_file is not None and  os.path.exists(keyword_file):
            with open(keyword_file, 'r', encoding='utf-8') as f:
                # 对文件内容进行json解析
                keywords = json.load(f)
                # 确保每个关键词项都有priority字段，如果没有则设置默认值
                for keyword in keywords:
                    if "priority" not in keyword:
                        keyword["priority"] = 1000
                # 对keywords进行排序
                self.keywords = sorted(keywords, key=lambda x: x.get("priority", 1000), reverse=True)

            if not keywords:
                raise ValueError("关键词文件为空")

    def _setup_directories(self):
        self.output_path.mkdir(parents=True, exist_ok=True)
        self.error_files_path = self.output_path / "error_files"

    async def check_health(self):
        """检查所有模型服务健康状态"""
        try:
            # 1. 检查LLM模型服务
            self.http_client.headers.update({"Authorization": f"Bearer {tool_settings.llm_config.api_key,}"})
            llm_response = await self.http_client.get(f"{tool_settings.llm_config.base_url}/models")
            if llm_response.status_code != 200:
                self.logger.warning("LLM 模型服务不可用")
            else:
                self.logger.info("LLM 模型服务正常")
        except Exception as e:
            self.logger.error(f"LLM 模型服务异常: {str(e)}")

    def _collect_files(self) -> List[Dict[str, Any]]:
        """收集需要摘要的文件"""
        task_data = []
        if self.input_path.is_file():
            task_data.append(self._create_task_data(str(self.input_path), True))
        else:
            task_data.extend(self._collect_directory_files())
        return task_data

    def _create_task_data(self, file_path: str, is_file: bool) -> Dict[str, Any]:

        file_path_obj = Path(file_path)
        # 安全检查：确保文件路径在允许的范围内
        try:
            file_path_obj.resolve().relative_to(self.input_path.resolve())
        except ValueError:
            self.logger.warning(f"文件路径超出允许范围: {file_path}")
            raise SecurityError(f"不安全的文件路径: {file_path}")
        if is_file:
            # 匹配关键词，获取优先级
            try:
                content = self.extract_document_content(file_path)
            except Exception as e:
                self.logger.error(f"文件 {file_path} 提取内容失败: {str(e)}")
                raise e
            name, priority = self._match_keywords(content)
            return {
                "type": "file",
                "file_path": str(file_path_obj),
                'error_files_path': str(self.error_files_path),
                'result_path': str(self.output_path.joinpath(name)),
                'priority': priority
            }
        #如果传入的是目录，则遍历目录下的所有文件
        all_files = []
        priority = 2000
        name = "未命名"
        # 递归获取所有文件
        for file_path in file_path_obj.rglob("*"):
            if file_path.is_file():
                try:
                    content = self.extract_document_content(str(file_path))
                except Exception as e:
                    self.logger.error(f"文件 {file_path} 提取内容失败: {str(e)}")
                    raise e
                n, p = self._match_keywords(content)
                if p < priority:
                    priority = p
                    name = n
                all_files.append(str(file_path))

        return {
            "type": "directory",
            "file_path": str(file_path_obj),
            'files': all_files,
            'error_files_path': str(self.error_files_path),
            'result_path': str(os.path.join(self.output_path, name, file_path_obj.name)),
            'priority': priority
        }


    def _collect_directory_files(self) -> List[Dict[str, Any]]:
        task_data = []
        for file_path in self.input_path.glob("*"):
            if file_path.is_file():
                task_data.append(self._create_task_data(str(file_path), True))
            else:
                task_data.append(self._create_task_data(str(file_path), False))

        return task_data

    def _process_files(self, task_data: List[Dict[str, Any]], call_func: Callable, progress_description: str):
        """批量处理文件，使用异步并发提高效率"""
        if not task_data:
            self.logger.info("没有文件需要处理")
            return

        self.logger.info(f"开始处理 {len(task_data)} 个文件")
        config = QueueConfig(
            max_workers=tool_settings.tool_config.limits,
            max_retries=2,
            progress_description=progress_description,
            show_progress=True,
            show_detailed_progress=True
        )

        # 创建队列并处理任务
        queue = AsyncTaskQueue(config)
        async def run_test():
            return await queue.process_tasks(call_func, task_data)

        run_async_task(run_test())

        # 显示结果摘要
        queue.display_summary()

    def _match_keywords(self, content: str) -> Tuple[str, int]:
        """匹配关键词，返回优先级和关键词"""
        if len(self.keywords) > 0 :
            for keyword in self.keywords:
                keys = keyword.get("keys", [])
                name = keyword.get("name", "未命名")
                priority = keyword.get("priority", 1000)
                matched = any(key in content for key in keys)
                if matched:
                    return name, priority

            return "未匹配到", 1000

        return "", 1000

    def extract_document_content(self, file_path: str):
        """提取文档内容"""
        content = ""
        file_ext = Path(file_path).suffix.lower()

        try:
            if file_ext == ".docx":
                # 提取Word文档内容
                doc = docx.Document(file_path)
                for para in doc.paragraphs:
                    content += para.text + "\n"

            elif file_ext in [".xlsx", ".xls"]:
                # 提取Excel文档内容
                df = pd.read_excel(file_path)
                content = df.to_string(index=False) + "\n"

            elif file_ext in [".ppt", ".pptx"]:
                # 提取PPT文档内容
                ppt = Presentation(file_path)
                for slide in ppt.slides:
                    for shape in slide.shapes:
                        if hasattr(shape, "text") and shape.text:
                            content += shape.text + "\n"

            elif file_ext == ".pdf":
                # 提取PDF文档内容
                doc = fitz.open(file_path)
                for page in doc:
                    content += page.get_text() + "\n"

            elif file_ext ==".html":
                with open(file_path, "r", encoding="utf-8") as f:
                    # soup = BeautifulSoup(f.read(), 'html.parser')
                    content = f.read().strip()

            else:
                raise ValueError("不支持的文件格式", file_path)
        except Exception as e:
            print(f"提取文档内容时出错: {str(e)}")

        return content


