import os.path
from pathlib import Path
from typing import Dict

from core.log import setup_logger
from modules.tool_base import ToolBase
from utils.translation.docx import translate_docx
from utils.translation.excel import excel_translate
from utils.translation.html import HTMLTranslator
from utils.translation.pdf import Pdf2zhTranslator
from utils.translation.power_point import ppt_translate


class DocumentTranslation(ToolBase):
    def __init__(self, input_path: str, output_path: str, keyword_file: str):
        super().__init__(input_path, output_path, setup_logger(__name__), keyword_file)
        self._setup_directories()
        self.html_translator = HTMLTranslator()

    def process(self):
        # 收集需要摘要的文件
        task_data = self._collect_files()
        self._process_files(task_data, self.process_file, "文档翻译任务")

    def process_file(self, task_data: Dict[str, str]):
        os.makedirs(task_data['result_path'], exist_ok=True)
        if task_data['type'] == 'file':
            output_file = os.path.join(task_data['result_path'],os.path.basename(task_data['file_path']))
            self.tran_document(task_data['file_path'], output_file)
        elif task_data['type'] == 'directory':
            for file_path in task_data['files']:
                output_file = os.path.join(task_data['result_path'],os.path.basename(file_path))
                self.tran_document(file_path, output_file)


    def tran_document(self, file_path: str, output_file: str):
        # 判断文档类型
        file_ext = Path(file_path).suffix.lower()
        if file_ext in ['.docx']:
            translate_docx(file_path, output_file)
        elif file_ext in ['.pdf']:
            try:
                pdf = Pdf2zhTranslator(file_path, os.path.dirname(output_file))
                pdf()
            except  Exception as e:
                if "The document contains no paragraphs" in str(e):
                    self.logger.warning(f"PDF文件 {file_path} 无内容")
                    return
                self.logger.error(f"翻译PDF文件 {file_path} 失败: {e}")
        elif file_ext in ['.xls', '.xlsx', 'csv']:
            excel_translate(file_path, output_file)
        elif file_ext in [".ppt", ".pptx"]:
            ppt_translate(file_path, output_file)
        elif file_ext in [".html"]:
            self.html_translator.translate_html(file_path, output_file)
        else:
            raise ValueError(f"不支持的文件类型: {file_ext}")

