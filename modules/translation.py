import asyncio
import os

import click
import re
from typing import Dict, Optional
from pathlib import Path

from core.registry import registry
from core.exceptions import *
from modules.translate import DocumentTranslation


@click.command()
@click.argument('file_path', type=click.Path(readable=True))
@click.option('-o', '--output', 'output_path',
              type=click.Path(writable=True),
              help='输出目录')
@click.option('-k', '--keywords', 'keywords_file',
              type=click.Path(readable=True),
              help='关键词文件路径')
@handle_exception
def translate(file_path: Optional[str], output_path: Optional[str], keywords_file: Optional[str]):
    """文档翻译功能 - 将文档翻译为简体中文，并保留原有格式"""
    if not output_path:
        raise ValueError("输出路径不能为空")
    if not os.path.exists(file_path):
        raise FileNotFoundError(f"输入路径不存在: {file_path}")
    # if not os.path.exists(keywords_file):
    #     raise FileNotFoundError(f"关键词文件不存在: {keywords_file}")

    try:
        tran = DocumentTranslation(file_path, output_path, keywords_file)
        asyncio.run(tran.check_health())
        tran.process()
    except Exception as e:
        raise e
    # click.echo(f"开始处理文本: {text}")


# 注册模块
registry.register(
    'translation',
    '文档翻译',
    '将文档翻译为简体中文，并保留原有格式',
    translate
)
