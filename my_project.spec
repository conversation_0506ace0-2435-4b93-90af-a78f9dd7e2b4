# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['main.py'],  # 主程序入口
    pathex=['./'],  # 项目根目录
    binaries=[],
    datas=[
        ('utils/*', 'utils'),  # 包含自定义工具包
        ('modules/*', 'modules'),
        ('core/*', 'core'),
        ('config/*', 'config'),  # 添加配置文件
        ('key.json', '.'),       # 添加密钥文件
        ('requirements.txt', '.'), # 添加依赖文件
    ],
    hiddenimports=[
        # 项目模块
        'modules.data_cleaning',
        'modules.translation',
        'modules.summarization',
        'core.cli_framework',
        'core.exceptions',

        # Windows COM组件
        'win32com.client',
        'pythoncom',

        # CLI框架
        'click',

        # Rich库组件
        'rich.console',
        'rich.progress',
        'rich.table',
        'rich.panel',
        'rich.text',

        # 数据处理
        'pandas',
        'openpyxl',
        'xlutils.copy',
        'xlrd',

        # 加密和安全
        'cryptography',
        'cryptography.fernet',
        'cryptography.hazmat.primitives',
        'cryptography.hazmat.backends.openssl',

        # 系统和网络
        'psutil',
        'websockets',
        'httpx',

        # 日志和配置
        'loguru',
        'pyyaml',
        'pydantic',
        'pydantic_settings',

        # AI和翻译
        'openai',
        'pdf2zh_next',

        # 文档处理
        'beautifulsoup4',
        'docx',
        'pptx',
        'fitz',  # PyMuPDF
        'ocrmypdf',

        # 压缩和加密检测
        'msoffcrypto',
        'rarfile',
        'py7zr',

        # 其他工具
        'tenacity',
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[
        'tkinter',
        'matplotlib',
        # 移除numpy排除，因为其他库可能需要它
    ],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)
pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='StarMap_CLI_Tool',  # 更具描述性的exe文件名
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,       # 使用UPX压缩
    upx_exclude=[   # 排除可能导致问题的DLL
        'vcruntime140.dll',
        'python3.dll',
        'python312.dll',
        '_ssl.pyd',
        '_hashlib.pyd',
        'libcrypto-1_1.dll',
        'libssl-1_1.dll',
    ],
    runtime_tmpdir=None,
    console=True,   # CLI程序保持为True
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=None,      # 可以添加图标文件路径
)
