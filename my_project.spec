# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['main.py'],  # 主程序入口
    pathex=['./'],  # 项目根目录
    binaries=[],
    datas=[
        ('utils/*', 'utils'),  # 包含自定义工具包
        ('modules/*', 'modules'),
        ('core/*', 'core')
    ],
    hiddenimports=[
        'modules.data_cleaning',
        'modules.translation',
        'modules.summarization',
        'core.cli_framework',
        'core.exceptions',
        'win32com.client',
        'pythoncom',
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[
        'tkinter',
        'matplotlib',
        'numpy',
    ],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)
pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='my_app',  # 生成的exe文件名
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,       # 使用UPX压缩
    upx_exclude=[],
    runtime_tmpdir=None,
    console=True,   # 如果是GUI程序，设为False
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
)
