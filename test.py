import pdf2zh_next
from pdf2zh_next import WatermarkOutputMode
#
if __name__ == '__main__':
    sett = pdf2zh_next.SettingsModel(
        basic=pdf2zh_next.BasicSettings(
            input_files={
                "clean/result/6e05186d-d124-4e6b-af88-dcd24d0dd489/0004.pdf"}
        ),
        translation=pdf2zh_next.TranslationSettings(

            qps=10,
            output="./out",
            no_auto_extract_glossary=True,
            custom_system_prompt="你是一个专业、真实的机器翻译引擎。\n仅输出翻译后的文本，不包括任何其他文本。\n确保忠实于原意，不改变逻辑、细节或语气，完整保留内容中的情感和表达方式。\n如果无法翻译，请直接输出原始内容，不需要多余的内容，也不需要解释其所表达的含义，采用文本到文本的输出方。\n请根据上下文选择适当的口语化表达或正式语气，使译文符合中文语言"
        ),
        pdf=pdf2zh_next.PDFSettings(
            no_mono=True,
            watermark_output_mode=WatermarkOutputMode.NoWatermark,
            skip_scanned_detection= True,
            ocr_workaround=True
        ),
        translate_engine_settings=pdf2zh_next.OpenAISettings(
            openai_api_key="iBYcdv999bDCSOyuNjorZvfaMEooeq1gRBHRwP5L7GGLGMqxBXrSntKsHTiP72vc",
            openai_base_url="http://*************:15555/chat/v1",
            openai_model="Qwen3-30B-A3B-GPTQ-Int4"
        )
    )

    pdf2zh_next.do_translate_file(sett)

# from pathlib import Path
# from docx_translator.translator import setup_openai_client, process_document
# import docx_translator
#
# # Set up OpenAI client
# client = setup_openai_client(base_url="http://*************:15555/chat/v1", api_key="iBYcdv999bDCSOyuNjorZvfaMEooeq1gRBHRwP5L7GGLGMqxBXrSntKsHTiP72vc")
# docx_translator.translator.DEFAULT_MODEL = "Qwen3-30B-A3B-GPTQ-Int4"
#
# # Translate document
# process_document(
#    input_file=Path("C:\\Users\\<USER>\\Desktop\\乌兹\\PF-37 21.02.2024.docx"),
#    output_file=Path("translated_document.docx"),
#    target_language="Chinese",
#    target_styles=["Normal", "Heading 1"],
#    openai_client=client,
#    parallel=True,
#    max_concurrent=10
# )