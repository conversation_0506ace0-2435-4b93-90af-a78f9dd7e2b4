import hashlib
import os
import zipfile

import fitz
import msoffcrypto
import rarfile
import py7zr


def is_office_encrypted(file_path):
    """
    通用判断 Word/Excel/PPT 文件是否加密（支持 doc/xls/ppt/docx/xlsx/pptx）
    """
    ext = os.path.splitext(file_path)[1].lower()

    try:
        with open(file_path, "rb") as f:
            office_file = msoffcrypto.OfficeFile(f)
            try:
                if office_file.format is None:
                    # msoffcrypto 无法识别格式（可能是旧版文件或格式问题）
                    return False
                return office_file.is_encrypted()
            except Exception as inner_e:
                # msoffcrypto 无法处理某些旧格式，但可以识别 header
                if "Unsupported file format" in str(inner_e):
                    print(f"[!] 不支持的文件格式: {file_path} -> {inner_e}")
                    return False
                raise  # 其他异常抛出
    except msoffcrypto.exceptions.EncryptionError:
        print(f"[!] 不支持的加密格式: {file_path}")
        return True
    except Exception as e:
        if "Unsupported EncryptionInfo" in str(e):
            return True
        # print(f"[!] Office 检查错误: {file_path} -> {e}")
        if ext == ".docx":
            return is_docx_encrypted(file_path)
        elif ext == ".xlsx":
            return is_xlsx_encrypted(file_path)
        elif ext == ".pptx":
            return is_pptx_encrypted(file_path)
        return False


def is_docx_encrypted(file_path):
    try:
        with zipfile.ZipFile(file_path, 'r') as zf:
            return 'word/document.xml' not in zf.namelist()
    except:
        return True  # 不是有效 ZIP，可能加密或损坏


def is_xlsx_encrypted(file_path):
    try:
        with zipfile.ZipFile(file_path, 'r') as zf:
            return 'xl/workbook.xml' not in zf.namelist()
    except:
        return True


def is_pptx_encrypted(file_path):
    try:
        with zipfile.ZipFile(file_path, 'r') as zf:
            return 'ppt/presentation.xml' not in zf.namelist()
    except:
        return True


def is_pdf_encrypted(file_path):
    """
    判断 PDF 是否加密
    """
    try:

        with open(file_path, "rb") as f:
            doc = fitz.Document(f)
            return doc.is_encrypted
    except Exception as e:
        print(f"[!] PDF 加密检查错误: {file_path} -> {e}")
        return False


def is_zip_encrypted(file_path):
    with zipfile.ZipFile(file_path) as zf:
        for zinfo in zf.infolist():
            # 检查每个文件的 flag_bits 的第0位是否为1（表示加密）
            if zinfo.flag_bits & 0x1:
                return True
    return False


def is_rar_encrypted(rar_path):
    rf = rarfile.RarFile(rar_path)
    for f in rf.infolist():
        if f.needs_password():
            return True
    return False


def is_7z_encrypted(path):
    try:
        with py7zr.SevenZipFile(path, mode='r') as archive:
            return archive.password_protected
    except py7zr.exceptions.PasswordRequired:
        return True  # 明确是加密文件
    except Exception as e:
        print(f"错误：{e}")
        return None  # 无法识别或损坏


def is_file_encrypted(file_path):
    """
    根据文件扩展名选择合适的判断方式
    """
    ext = os.path.splitext(file_path)[1].lower()

    if ext in ['.doc', '.xls', '.ppt', '.docx', '.xlsx', '.pptx']:
        return is_office_encrypted(file_path)

    elif ext == '.docx':
        # 可选结构检查
        return is_docx_encrypted(file_path)
    elif ext == '.pdf':
        return is_pdf_encrypted(file_path)
    elif ext == '.zip':
        return is_zip_encrypted(file_path)
    elif ext == '.rar':
        return is_rar_encrypted(file_path)
    elif ext == '.7z':
        return is_7z_encrypted(file_path)

    else:
        return False
        # raise ValueError(f"不支持的文件类型: {ext}")


def calculate_file_hash(file_path):
    """
    计算文件的SHA-256哈希值

    Args:
        file_path (str): 文件路径

    Returns:
        str: 文件的哈希值（十六进制字符串）
    """
    sha256_hash = hashlib.sha256()

    with open(file_path, "rb") as f:
        # 分块读取文件以处理大文件
        for byte_block in iter(lambda: f.read(4096), b""):
            sha256_hash.update(byte_block)

    return sha256_hash.hexdigest()
