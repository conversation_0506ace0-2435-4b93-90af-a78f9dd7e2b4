import shutil

import py7zr
import rarfile
import os
import zipfile
import xml.etree.ElementTree as ET
from olefile import OleFileIO
import argparse
import sys
import re
from datetime import datetime
import logging


def copy_file(source_path: str, destination_path: str) -> bool:
    """复制文件"""
    os.makedirs(os.path.dirname(destination_path), exist_ok=True)
    try:
        # with open(source_path, 'rb') as source_file:
        #     with open(destination_path, 'wb') as dest_file:
        #         dest_file.write(source_file.read())
        shutil.copy2(source_path, destination_path)
        return True
    except Exception as e:
        print(f"复制文件失败: {e}")
        return False


def extract_zip_rar_7z(zip_path, output_dir) -> list:
    """
    解压zip, rar, 7z压缩包

    Args:
        zip_path: 压缩包路径
        output_dir: 输出目录
    Return:
        files: 解压后的文件列表
    """
    # 输出目录一压缩包明明
    os.makedirs(output_dir, exist_ok=True)

    ext = os.path.splitext(zip_path)[1].lower()

    if ext == '.zip':
        with zipfile.ZipFile(zip_path, 'r') as zip_ref:
            zip_ref.extractall(output_dir)
    elif ext == '.rar':
        with rarfile.RarFile(zip_path, 'r') as rar_ref:
            rar_ref.extractall(output_dir)
    elif ext == '.7z':
        with py7zr.SevenZipFile(zip_path, mode='r') as archive:
            archive.extractall(output_dir)
    unpack_files = []
    for root, dirs, files in os.walk(output_dir):
        for file in files:
            unpack_files.append(os.path.join(root, file))
    return unpack_files



def get_document_metadata(file_path):
    """获取文档的元数据（支持Office和PDF）"""
    if not os.path.isfile(file_path):
        raise FileNotFoundError(f"文件不存在: {file_path}")

    ext = os.path.splitext(file_path)[1].lower()

    # 处理新版Office文档 (OpenXML格式)
    if ext in ('.docx', '.xlsx', '.pptx'):
        return get_openxml_metadata(file_path)

    # 处理旧版Office文档 (OLE格式)
    # elif ext in ('.doc', '.xls', '.ppt'):
    #     return get_ole_metadata(file_path)

    else:
        return {}


def get_openxml_metadata(file_path):
    """提取OpenXML格式(.docx, .xlsx, .pptx)文档的元数据"""
    metadata = {
        '作者': None, '公司': None, '标题': None, '主题': None,
        '创建时间': None, '修改时间': None, '最后保存者': None
    }

    try:
        with zipfile.ZipFile(file_path) as zf:
            # 解析核心元数据 (core.xml)
            if 'docProps/core.xml' in zf.namelist():
                core_xml = zf.read('docProps/core.xml')
                root = ET.fromstring(core_xml)

                # 命名空间定义
                ns = {
                    'cp': 'http://schemas.openxmlformats.org/package/2006/metadata/core-properties',
                    'dc': 'http://purl.org/dc/elements/1.1/',
                    'dcterms': 'http://purl.org/dc/terms/',
                    'xsi': 'http://www.w3.org/2001/XMLSchema-instance'
                }

                # 提取核心元数据
                metadata['作者'] = safe_find(root, './/dc:creator', ns)
                metadata['标题'] = safe_find(root, './/dc:title', ns)
                metadata['主题'] = safe_find(root, './/dc:subject', ns)
                metadata['创建时间'] = safe_find(root, './/dcterms:created', ns)
                metadata['修改时间'] = safe_find(root, './/dcterms:modified', ns)
                metadata['最后保存者'] = safe_find(root, './/cp:lastModifiedBy', ns)

            # 解析应用元数据 (app.xml) - 包含公司信息
            if 'docProps/app.xml' in zf.namelist():
                app_xml = zf.read('docProps/app.xml')
                root = ET.fromstring(app_xml)
                ns = {'': 'http://schemas.openxmlformats.org/officeDocument/2006/extended-properties'}
                metadata['公司'] = safe_find(root, './/Company', ns)

    except Exception as e:
        raise RuntimeError(f"解析OpenXML文档失败: {str(e)}")

    return metadata


def get_ole_metadata(file_path):
    """提取OLE格式(.doc, .xls, .ppt)文档的元数据"""
    metadata = {
        '作者': None, '公司': None, '标题': None, '主题': None,
        '创建时间': None, '修改时间': None, '最后保存者': None
    }

    try:
        ole = OleFileIO(file_path)
        if not ole.exists('\\x05DocumentSummaryInformation'):
            raise ValueError("文件不是有效的OLE文档")

        try:
            meta = ole.get_metadata()

            # 提取标准属性
            metadata['作者'] = safe_decode(meta.author)
            metadata['标题'] = safe_decode(meta.title)
            metadata['主题'] = safe_decode(meta.subject)
            metadata['最后保存者'] = safe_decode(meta.last_saved_by)

            # 提取公司信息 - 可能在多个位置
            metadata['公司'] = safe_decode(meta.company)
            if not metadata['公司']:
                # 尝试从其他位置获取公司信息
                metadata['公司'] = safe_decode(meta.manager) or safe_decode(meta.company)

            # 时间信息转换
            metadata['创建时间'] = safe_decode(meta.create_time)
            metadata['修改时间'] = safe_decode(meta.last_saved_time)

        finally:
            ole.close()

    except Exception as e:
        logging.warning(f"OLE文档元数据提取失败: {str(e)}")
        # 返回空元数据而不是抛出异常
        return metadata

    return metadata




def safe_find(root, path, namespaces):
    """安全查找XML元素"""
    element = root.find(path, namespaces)
    return element.text if element is not None else None


def safe_decode(byte_str):
    """安全解码字节字符串"""
    if byte_str is None:
        return None
    try:
        return byte_str.decode('utf-8', errors='ignore')
    except:
        return str(byte_str)


def print_metadata(metadata, file_path):
    """格式化输出元数据"""
    print(f"\n文档元数据: {os.path.basename(file_path)}")
    print("-" * 60)
    for key, value in metadata.items():
        if value:
            print(f"{key}: {value}")
    print("-" * 60)


def main():
    parser = argparse.ArgumentParser(description='提取文档元数据（支持Office和PDF）')
    parser.add_argument('file', nargs='+', help='文档路径')
    parser.add_argument('--csv', action='store_true', help='以CSV格式输出结果')
    args = parser.parse_args()

    # CSV格式输出
    if args.csv:
        print("文件名,作者,公司,标题,主题,创建时间,修改时间,最后保存者")

    for file_path in args.file:
        try:
            metadata = get_document_metadata(file_path)

            if args.csv:
                # CSV格式输出
                values = [os.path.basename(file_path)]
                for key in ['作者', '公司', '标题', '主题', '创建时间', '修改时间', '最后保存者']:
                    values.append(str(metadata.get(key, '')).replace(',', ';'))
                print(','.join(values))
            else:
                # 标准格式输出
                print_metadata(metadata, file_path)

        except Exception as e:
            print(f"\n处理文件 {file_path} 时出错: {str(e)}", file=sys.stderr)


if __name__ == "__main__":
    main()