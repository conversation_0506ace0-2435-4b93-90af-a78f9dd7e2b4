import email
import os
import re
import uuid
from email import policy
from email.header import decode_header, make_header
from email.utils import getaddresses
from typing import Dict, Any


def parse_eml(file_path: str) -> Dict[str, Any]:
    """解析.eml格式邮件"""
    with open(file_path, 'rb') as f:
        email_content = f.read()
    msg = email.message_from_bytes(email_content, policy=policy.default)
    attachments = []
    # 处理附件
    for part in msg.iter_attachments():
        raw_name = part.get_filename() or ''
        filename = safe_filename(raw_name)
        attachment_info = {
            'filename': filename,
            'content_type': part.get_content_type(),
        }
        file_ext = os.path.splitext(filename)[1] or ''
        # 特殊处理message/rfc822类型的附件（嵌套的email）
        if part.get_content_type() == 'message/rfc822':
            # 对于嵌套的email，我们不需要decode=True
            payload = part.get_payload()
            if payload and isinstance(payload, list) and len(payload) > 0:
                # 嵌套email通常作为列表返回
                nested_email = payload[0]
                attachment_info['content'] = bytes(nested_email)
        else:
            # 普通附件处理
            payload = part.get_payload(decode=True)
            if payload is not None:
                attachment_info['content'] = payload
        # 判断是否加密
        attachments.append(attachment_info)

    # # 整合一下邮件的 收发件人，正文内容 信息为html
    subject = msg.get('subject', '')
    sender = get_email_field(msg, 'from')
    to = get_email_field(msg, 'to')
    cc = get_email_field(msg, 'cc')
    date = msg.get('date', '')
    html_body = get_email_body(msg, 'html') or ''
    body = get_email_body(msg, 'plain') or ''
    #
    # # 生成HTML格式的邮件内容
    mail_html = _generate_mail_html(subject, sender, to, cc, date, html_body, body)

    return {
        'mail_html': mail_html,
        'attachments': attachments
    }


def _generate_mail_html(subject: str, sender: str, to: str, cc: str, date: str,
                        html_body: str, plain_body: str) -> str:
    """生成邮件的HTML格式内容"""

    # HTML转义函数
    def escape_html(text: str) -> str:
        if not text:
            return ''
        return (text.replace('&', '&amp;')
                .replace('<', '&lt;')
                .replace('>', '&gt;')
                .replace('"', '&quot;')
                .replace("'", '&#x27;'))

    # 邮件正文内容（优先使用HTML格式，否则使用纯文本）
    body_content = _sanitize_html_body(
        html_body) if html_body and html_body.strip() else f'<div style="white-space: pre;">{escape_html(plain_body).strip()}</div>'

    # 生成完整的HTML模板
    html_template = f"""
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>邮件内容</title>
    <style>
        body {{ font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }}
        .email-header {{ background-color: #f5f5f5; padding: 15px; border-radius: 5px; margin-bottom: 20px; }}
        .email-field {{ margin-bottom: 8px; }}
        .email-field strong {{ color: #333; min-width: 80px; display: inline-block; }}
        .email-body {{ border: 1px solid #ddd; padding: 15px; border-radius: 5px; }}
        .attachments {{ margin-top: 20px; padding: 10px; background-color: #f9f9f9; border-radius: 5px; }}
        .attachments ul {{ margin: 5px 0; padding-left: 20px; }}
    </style>
</head>
<body>
    <div class="email-header">
        <div class="email-field"><strong>主题:</strong> {escape_html(subject)}</div>
        <div class="email-field"><strong>发件人:</strong> {escape_html(sender)}</div>
        <div class="email-field"><strong>收件人:</strong> {escape_html(to)}</div>
        {f'<div class="email-field"><strong>抄送:</strong> {escape_html(cc)}</div>' if cc else ''}
        <div class="email-field"><strong>日期:</strong> {escape_html(date)}</div>
    </div>

    <div class="email-body">
        {body_content}
    </div>
</body>
</html>
    """.strip()

    return html_template


def _sanitize_html_body(html_content: str) -> str:
    """安全处理邮件HTML正文内容"""
    if not html_content or not html_content.strip():
        return ''

    # 移除JavaScript相关内容
    # 1. 移除script标签及其内容
    html_content = re.sub(r'<script[^>]*>.*?</script>', '', html_content, flags=re.DOTALL | re.IGNORECASE)

    # 2. 移除内联JavaScript事件处理器
    js_events = ['onclick', 'onload', 'onmouseover', 'onmouseout', 'onfocus', 'onblur',
                 'onchange', 'onsubmit', 'onkeydown', 'onkeyup', 'onkeypress', 'onerror']
    for event in js_events:
        html_content = re.sub(rf'\s{event}\s*=\s*["\'][^"\']*["\']', '', html_content, flags=re.IGNORECASE)

    # 3. 移除javascript:协议的链接
    html_content = re.sub(r'href\s*=\s*["\']javascript:[^"\']*["\']', 'href="#"', html_content, flags=re.IGNORECASE)

    # 4. 移除潜在危险标签
    dangerous_tags = ['iframe', 'object', 'embed', 'form', 'input', 'button', 'textarea', 'select']
    for tag in dangerous_tags:
        html_content = re.sub(rf'<{tag}[^>]*>.*?</{tag}>', '', html_content, flags=re.DOTALL | re.IGNORECASE)
        html_content = re.sub(rf'<{tag}[^>]*/?>', '', html_content, flags=re.IGNORECASE)

    # 提取body内容
    body_match = re.search(r'<body[^>]*>(.*?)</body>', html_content, flags=re.DOTALL | re.IGNORECASE)
    if body_match:
        html_content = body_match.group(1)

    # 清理后检查是否还有有效内容
    # 移除HTML标签后检查是否有实际文本内容
    text_content = re.sub(r'<[^>]+>', '', html_content).strip()
    if not text_content:
        return ''

    # 确保HTML结构完整性，添加基本的容器
    html_content = html_content.strip()
    if html_content and not html_content.startswith('<'):
        html_content = f'<div>{html_content}</div>'

    return html_content
def get_email_field(msg, field_name: str, as_addresses: bool = False):
    """
    安全获取邮件头字段并可选解析为地址列表。

    参数
    ----
    msg : email.message.Message | EmailMessage
        已经解析好的邮件对象。
    field_name : str
        头字段名，例如 'cc'、'to'、'bcc'、'from'。
    as_addresses : bool, default False
        - False：返回一个已经解码、去除换行的字符串。
        - True：  返回 List[Tuple[str, str]] 形式，等同于 email.utils.getaddresses。

    返回
    ----
    str | list[tuple[str, str]]
        - 字符串：适合直接打印、日志或后续解析。
        - 列表：  每个元素是 (display_name, email_address)。
    """
    # 编译一次正则：ASCII 控制字符（包含 \r \n \t 等）
    _CONTROL_CHARS_RE = re.compile(r'[\x00-\x1F]+')
    # 1. 取原始头字段；不存在就返回空
    try:
        raw_value = msg.get(field_name, '')
    except:
        raw_value = ''
    if not raw_value:
        return [] if as_addresses else ''

    # 2. RFC2047 / RFC2231 解码
    try:
        decoded = str(make_header(decode_header(raw_value)))
    except Exception:
        # 解码失败时保留原值，避免抛异常中断主流程
        decoded = raw_value

    # 3. 去掉控制字符，再把多余空白压成单空格
    cleaned = _CONTROL_CHARS_RE.sub('', decoded)        # 移除 \r \n \t 等
    cleaned = re.sub(r'\s*\n\s*', ' ', cleaned)         # 折叠因软换行留下的空白
    cleaned = re.sub(r'\s{2,}', ' ', cleaned).strip()   # 压缩多空格

    # 4. 需要结构化地址则调用 getaddresses
    if as_addresses:
        return getaddresses([cleaned])
    else:
        return cleaned

# —— 1. 将文件名解码并做安全清洗 ——
def safe_filename(raw_name: str, fallback: str = 'attachment') -> str:
    # RFC2047 / RFC2231 解码
    try:
        raw_name = str(make_header(decode_header(raw_name)))
    except Exception:
        pass                     # 如果解码失败就保留原值

    if not raw_name:
        raw_name = fallback

    # 去掉隐藏控制字符（\0-\31），尤其是 \r \n
    raw_name = re.sub(r'[\x00-\x1F]', '', raw_name)

    # Windows 不允许的字符 : * ? " < > | / \
    raw_name = re.sub(r'[<>:"/\\|?*]', '_', raw_name).strip()

    # 避免生成 ''  或  '.'  之类路径
    return raw_name or f'{fallback}_{uuid.uuid4().hex}'


def get_email_body( msg: email.message.Message, content_type: str) -> str:
    """获取邮件正文"""
    if msg.is_multipart():
        for part in msg.walk():
            if part.get_content_type() == f'text/{content_type}':
                return get_content(part)
    elif msg.get_content_type() == f'text/{content_type}':
        return get_content(msg)
    return ''

def get_content(part) -> str:
    """获取邮件内容"""
    try:
        payload = part.get_payload(decode=True)
        if payload is not None:
            return payload.decode()
        else:
            return ''
    except:
        try:
            return part.get_content()
        except:
            return ''