import gc
import os
from contextlib import contextmanager
from typing import Dict, List, Any, Optional, Iterator, Tuple

import docx
from docx.shared import RGBColor
from docx.enum.text import WD_UNDERLINE, WD_ALIGN_PARAGRAPH
from core.log import setup_logger
from utils.translation.translate import openaiTranslate

log = setup_logger(__name__)


@contextmanager
def memory_efficient_processing():
    """内存高效处理上下文管理器"""
    try:
        # 处理前清理内存
        gc.collect()
        initial_objects = len(gc.get_objects())
        # log.debug(f"开始处理，当前对象数量: {initial_objects}")
        yield
    finally:
        # 处理后清理内存
        gc.collect()
        final_objects = len(gc.get_objects())
        # log.debug(f"处理完成，当前对象数量: {final_objects}, 变化: {final_objects - initial_objects}")


class MemoryManager:
    """内存管理器，监控和优化内存使用"""

    def __init__(self, max_memory_mb: int = 500):
        self.max_memory_mb = max_memory_mb
        self.processed_batches = 0

    def get_memory_usage_mb(self) -> float:
        """获取当前内存使用量（MB）"""
        try:
            import psutil
            process = psutil.Process()
            return process.memory_info().rss / 1024 / 1024
        except ImportError:
            # 如果没有psutil，使用gc统计对象数量作为粗略估计
            return len(gc.get_objects()) / 10000  # 粗略估计

    def should_cleanup(self) -> bool:
        """判断是否需要进行内存清理"""
        current_memory = self.get_memory_usage_mb()
        return current_memory > self.max_memory_mb

    def cleanup_memory(self):
        """执行内存清理"""
        before_memory = self.get_memory_usage_mb()
        gc.collect()
        after_memory = self.get_memory_usage_mb()
        freed_memory = before_memory - after_memory

        # log.info(f"内存清理完成: {before_memory:.1f}MB -> {after_memory:.1f}MB (释放 {freed_memory:.1f}MB)")

        self.processed_batches += 1
        # if self.processed_batches % 10 == 0:
        #     log.info(f"已处理 {self.processed_batches} 个批次")

    def force_cleanup_if_needed(self):
        """如果需要，强制执行内存清理"""
        if self.should_cleanup():
            # log.warning(f"内存使用过高 ({self.get_memory_usage_mb():.1f}MB)，执行强制清理")
            self.cleanup_memory()


class StreamingDocumentProcessor:
    """流式文档处理器，支持大文档的分块处理"""

    def __init__(self, chunk_size: int = 50, memory_limit_mb: int = 500):
        self.chunk_size = chunk_size
        self.memory_manager = MemoryManager(memory_limit_mb)
        self.translation_cache = {}

    def generate_cache_key(self, content: str) -> str:
        """生成内容的缓存键"""
        import hashlib
        return hashlib.md5(content.encode('utf-8')).hexdigest()[:16]

    def process_content_chunks(self, content_items: List[Dict[str, Any]]) -> Iterator[List[Dict[str, Any]]]:
        """将内容分块处理，返回批次迭代器"""
        total_items = len(content_items)
        # log.info(f"开始分块处理 {total_items} 个内容项，块大小: {self.chunk_size}")

        for i in range(0, total_items, self.chunk_size):
            chunk = content_items[i:i + self.chunk_size]
            chunk_info = {
                'chunk_index': i // self.chunk_size,
                'chunk_size': len(chunk),
                'total_chunks': (total_items + self.chunk_size - 1) // self.chunk_size,
                'items': chunk
            }

            # log.debug(f"处理块 {chunk_info['chunk_index'] + 1}/{chunk_info['total_chunks']}, "
            #          f"包含 {chunk_info['chunk_size']} 个项目")

            yield chunk_info

            # 每处理一个块后检查内存
            self.memory_manager.force_cleanup_if_needed()

    def translate_chunk_with_cache(self, texts: List[str]) -> List[str]:
        """带缓存的块翻译"""
        results = []
        texts_to_translate = []
        text_indices = []

        # 检查缓存
        for i, text in enumerate(texts):
            cache_key = self.generate_cache_key(text)
            if cache_key in self.translation_cache:
                results.append(self.translation_cache[cache_key])
                log.debug(f"使用缓存翻译: {text[:30]}...")
            else:
                results.append(None)  # 占位符
                texts_to_translate.append(text)
                text_indices.append(i)

        # 翻译未缓存的文本
        if texts_to_translate:
            # log.debug(f"翻译 {len(texts_to_translate)} 个新文本")
            translated_texts = openaiTranslate.translate(texts_to_translate)

            # 更新结果和缓存
            for idx, translated_text in zip(text_indices, translated_texts):
                results[idx] = translated_text
                cache_key = self.generate_cache_key(texts[idx])
                self.translation_cache[cache_key] = translated_text

        return results


class DocxStyleManager:
    """DOCX样式管理器，负责完整的样式保留和恢复"""

    @staticmethod
    def preserve_complete_run_style(run) -> Dict[str, Any]:
        """完整保留run的所有样式属性"""
        try:
            style_dict = {
                'text': run.text,
                'bold': run.bold,
                'italic': run.italic,
                'font_name': run.font.name,
                'font_size': run.font.size,
                'font_color': None,
                'underline': None,
                'strike': run.font.strike if hasattr(run.font, 'strike') else None,
                'superscript': None,
                'subscript': None,
                'highlight_color': None,
                'all_caps': run.font.all_caps if hasattr(run.font, 'all_caps') else None,
                'small_caps': run.font.small_caps if hasattr(run.font, 'small_caps') else None,
                'shadow': run.font.shadow if hasattr(run.font, 'shadow') else None,
                'emboss': run.font.emboss if hasattr(run.font, 'emboss') else None,
                'imprint': run.font.imprint if hasattr(run.font, 'imprint') else None,
                'outline': run.font.outline if hasattr(run.font, 'outline') else None,
            }

            # 安全获取颜色信息
            if run.font.color and run.font.color.rgb:
                style_dict['font_color'] = run.font.color.rgb

            # 安全获取下划线信息
            if hasattr(run.font, 'underline') and run.font.underline:
                style_dict['underline'] = run.font.underline

            # 安全获取上标下标信息
            if hasattr(run.font, 'superscript') and run.font.superscript:
                style_dict['superscript'] = run.font.superscript
            if hasattr(run.font, 'subscript') and run.font.subscript:
                style_dict['subscript'] = run.font.subscript

            # 安全获取高亮颜色
            if hasattr(run.font, 'highlight_color') and run.font.highlight_color:
                style_dict['highlight_color'] = run.font.highlight_color

            return style_dict
        except Exception as e:
            # log.warning(f"保留run样式时出错: {str(e)}")
            return {
                'text': run.text,
                'bold': run.bold,
                'italic': run.italic,
                'font_name': run.font.name,
                'font_size': run.font.size,
                'font_color': None,
            }

    @staticmethod
    def preserve_paragraph_style(paragraph) -> Dict[str, Any]:
        """保留段落级样式"""
        try:
            style_dict = {
                'style': paragraph.style,
                'alignment': paragraph.alignment,
            }

            # 安全获取段落格式信息
            if hasattr(paragraph, 'paragraph_format') and paragraph.paragraph_format:
                pf = paragraph.paragraph_format
                style_dict.update({
                    'left_indent': pf.left_indent,
                    'right_indent': pf.right_indent,
                    'first_line_indent': pf.first_line_indent,
                    'space_before': pf.space_before,
                    'space_after': pf.space_after,
                    'line_spacing': pf.line_spacing,
                    'keep_together': pf.keep_together if hasattr(pf, 'keep_together') else None,
                    'keep_with_next': pf.keep_with_next if hasattr(pf, 'keep_with_next') else None,
                    'page_break_before': pf.page_break_before if hasattr(pf, 'page_break_before') else None,
                    'widow_control': pf.widow_control if hasattr(pf, 'widow_control') else None,
                })

            return style_dict
        except Exception as e:
            # log.warning(f"保留段落样式时出错: {str(e)}")
            return {'style': paragraph.style, 'alignment': paragraph.alignment}

    @staticmethod
    def apply_run_style(run, style_dict: Dict[str, Any]):
        """应用样式到run"""
        try:
            # 应用基本样式
            if 'bold' in style_dict and style_dict['bold'] is not None:
                run.bold = style_dict['bold']
            if 'italic' in style_dict and style_dict['italic'] is not None:
                run.italic = style_dict['italic']
            if 'font_name' in style_dict and style_dict['font_name']:
                run.font.name = style_dict['font_name']
            if 'font_size' in style_dict and style_dict['font_size']:
                run.font.size = style_dict['font_size']

            # 应用颜色
            if 'font_color' in style_dict and style_dict['font_color']:
                run.font.color.rgb = style_dict['font_color']

            # 应用下划线
            if 'underline' in style_dict and style_dict['underline']:
                run.font.underline = style_dict['underline']

            # 应用删除线
            if 'strike' in style_dict and style_dict['strike'] is not None:
                if hasattr(run.font, 'strike'):
                    run.font.strike = style_dict['strike']

            # 应用上标下标
            if 'superscript' in style_dict and style_dict['superscript'] is not None:
                if hasattr(run.font, 'superscript'):
                    run.font.superscript = style_dict['superscript']
            if 'subscript' in style_dict and style_dict['subscript'] is not None:
                if hasattr(run.font, 'subscript'):
                    run.font.subscript = style_dict['subscript']

            # 应用其他样式
            advanced_styles = ['all_caps', 'small_caps', 'shadow', 'emboss', 'imprint', 'outline', 'highlight_color']
            for style_name in advanced_styles:
                if style_name in style_dict and style_dict[style_name] is not None:
                    if hasattr(run.font, style_name):
                        setattr(run.font, style_name, style_dict[style_name])

        except Exception as e:
            log.warning(f"应用run样式时出错: {str(e)}")

    @staticmethod
    def apply_paragraph_style(paragraph, style_dict: Dict[str, Any]):
        """应用段落样式"""
        try:
            # 应用段落样式
            if 'style' in style_dict and style_dict['style']:
                paragraph.style = style_dict['style']
            if 'alignment' in style_dict and style_dict['alignment'] is not None:
                paragraph.alignment = style_dict['alignment']

            # 应用段落格式
            if hasattr(paragraph, 'paragraph_format') and paragraph.paragraph_format:
                pf = paragraph.paragraph_format
                format_attrs = ['left_indent', 'right_indent', 'first_line_indent',
                              'space_before', 'space_after', 'line_spacing',
                              'keep_together', 'keep_with_next', 'page_break_before', 'widow_control']

                for attr in format_attrs:
                    if attr in style_dict and style_dict[attr] is not None:
                        if hasattr(pf, attr):
                            setattr(pf, attr, style_dict[attr])

        except Exception as e:
            log.warning(f"应用段落样式时出错: {str(e)}")


class TableStyleManager:
    """表格样式管理器，专门处理表格结构和样式的保留"""

    @staticmethod
    def preserve_table_structure(table) -> Dict[str, Any]:
        """保留表格的完整结构信息"""
        try:
            table_info = {
                'table_style': table.style,
                'table_width': table.width if hasattr(table, 'width') else None,
                'alignment': table.alignment if hasattr(table, 'alignment') else None,
                'rows': [],
                'columns': []
            }

            # 保留列信息
            for col_idx, column in enumerate(table.columns):
                col_info = {
                    'index': col_idx,
                    'width': column.width if hasattr(column, 'width') else None,
                }
                table_info['columns'].append(col_info)

            # 保留行信息
            for row_idx, row in enumerate(table.rows):
                row_info = {
                    'index': row_idx,
                    'height': row.height if hasattr(row, 'height') else None,
                    'cells': []
                }

                # 保留单元格信息
                for cell_idx, cell in enumerate(row.cells):
                    cell_info = {
                        'index': cell_idx,
                        'width': cell.width if hasattr(cell, 'width') else None,
                        'vertical_alignment': cell.vertical_alignment if hasattr(cell, 'vertical_alignment') else None,
                        'margin_left': None,
                        'margin_right': None,
                        'margin_top': None,
                        'margin_bottom': None,
                    }

                    # 安全获取单元格边距
                    try:
                        if hasattr(cell, 'margin_left'):
                            cell_info['margin_left'] = cell.margin_left
                        if hasattr(cell, 'margin_right'):
                            cell_info['margin_right'] = cell.margin_right
                        if hasattr(cell, 'margin_top'):
                            cell_info['margin_top'] = cell.margin_top
                        if hasattr(cell, 'margin_bottom'):
                            cell_info['margin_bottom'] = cell.margin_bottom
                    except Exception:
                        pass  # 忽略边距获取错误

                    row_info['cells'].append(cell_info)

                table_info['rows'].append(row_info)

            return table_info

        except Exception as e:
            log.warning(f"保留表格结构时出错: {str(e)}")
            return {
                'table_style': None,
                'table_width': None,
                'alignment': None,
                'rows': [],
                'columns': []
            }

    @staticmethod
    def apply_table_structure(table, table_info: Dict[str, Any]):
        """应用表格结构信息"""
        try:
            # 应用表格级别的属性
            if table_info.get('table_style'):
                try:
                    table.style = table_info['table_style']
                except Exception:
                    pass  # 忽略样式应用错误

            if table_info.get('table_width'):
                try:
                    table.width = table_info['table_width']
                except Exception:
                    pass

            if table_info.get('alignment') is not None:
                try:
                    table.alignment = table_info['alignment']
                except Exception:
                    pass

            # 应用列宽
            columns_info = table_info.get('columns', [])
            for col_idx, col_info in enumerate(columns_info):
                if col_idx < len(table.columns) and col_info.get('width'):
                    try:
                        table.columns[col_idx].width = col_info['width']
                    except Exception:
                        pass

            # 应用行高和单元格属性
            rows_info = table_info.get('rows', [])
            for row_idx, row_info in enumerate(rows_info):
                if row_idx >= len(table.rows):
                    break

                row = table.rows[row_idx]

                # 应用行高
                if row_info.get('height'):
                    try:
                        row.height = row_info['height']
                    except Exception:
                        pass

                # 应用单元格属性
                cells_info = row_info.get('cells', [])
                for cell_idx, cell_info in enumerate(cells_info):
                    if cell_idx >= len(row.cells):
                        break

                    cell = row.cells[cell_idx]

                    # 应用单元格宽度
                    if cell_info.get('width'):
                        try:
                            cell.width = cell_info['width']
                        except Exception:
                            pass

                    # 应用垂直对齐
                    if cell_info.get('vertical_alignment') is not None:
                        try:
                            cell.vertical_alignment = cell_info['vertical_alignment']
                        except Exception:
                            pass

                    # 应用边距
                    margin_attrs = ['margin_left', 'margin_right', 'margin_top', 'margin_bottom']
                    for margin_attr in margin_attrs:
                        if cell_info.get(margin_attr) is not None:
                            try:
                                setattr(cell, margin_attr, cell_info[margin_attr])
                            except Exception:
                                pass

        except Exception as e:
            log.warning(f"应用表格结构时出错: {str(e)}")

    @staticmethod
    def preserve_cell_content_and_style(cell) -> Dict[str, Any]:
        """保留单元格内容和样式的完整信息"""
        try:
            cell_data = {
                'original_text': cell.text,
                'paragraphs': []
            }

            # 保留每个段落的信息
            for para in cell.paragraphs:
                para_info = {
                    'text': para.text,
                    'paragraph_style': DocxStyleManager.preserve_paragraph_style(para),
                    'run_styles': []
                }

                # 保留每个run的样式
                for run in para.runs:
                    run_style = DocxStyleManager.preserve_complete_run_style(run)
                    para_info['run_styles'].append(run_style)

                cell_data['paragraphs'].append(para_info)

            return cell_data

        except Exception as e:
            log.warning(f"保留单元格内容和样式时出错: {str(e)}")
            return {
                'original_text': cell.text if hasattr(cell, 'text') else '',
                'paragraphs': []
            }

    @staticmethod
    def apply_cell_content_and_style(cell, cell_data: Dict[str, Any], translated_text: str):
        """应用单元格内容和样式"""
        try:
            # 清空单元格内容，但保留结构
            for para in cell.paragraphs:
                para.clear()

            # 如果有保存的段落信息，使用第一个段落
            if cell_data.get('paragraphs'):
                first_para_info = cell_data['paragraphs'][0]
                first_para = cell.paragraphs[0]

                # 恢复段落样式
                DocxStyleManager.apply_paragraph_style(first_para, first_para_info['paragraph_style'])

                # 添加翻译文本并应用样式
                if first_para_info.get('run_styles'):
                    run = first_para.add_run(translated_text)
                    # 使用第一个有效的run样式
                    main_style = first_para_info['run_styles'][0]
                    for style in first_para_info['run_styles']:
                        if style.get('text', '').strip():
                            main_style = style
                            break
                    DocxStyleManager.apply_run_style(run, main_style)
                else:
                    first_para.add_run(translated_text)
            else:
                # 如果没有样式信息，直接添加文本
                cell.paragraphs[0].add_run(translated_text)

        except Exception as e:
            log.error(f"应用单元格内容和样式时出错: {str(e)}")
            # 降级处理：直接设置文本
            try:
                cell.text = translated_text
            except Exception:
                pass


def is_numeric_or_currency(text):
    """检查文本是否为数字或货币格式"""
    if not isinstance(text, str):
        return True
    text = str(text).strip()
    # 移除货币符号和逗号
    text = text.replace('₱', '').replace(',', '').replace('P', '')
    try:
        float(text)
        return True
    except ValueError:
        return False
def process_table_cell(text):
    """处理表格单元格文本，分离数字和文本部分"""
    if not text.strip():
        return text

    # 如果是纯数字或货币金额，直接返回
    if is_numeric_or_currency(text):
        return text

    # 处理包含 "Sub Total" 或类似文本的单元格
    if "Sub Total" in text or "Total" in text:
        return text

    return text

def paras_generator(all_paras, batch_size=2):
    all_paras_new = [para for para in all_paras if len(para.text.strip()) > 0]
    total = len(all_paras_new)
    for start in range(0, total, batch_size):
        end = start + batch_size
        end = total if end > total else end
        yield all_paras_new[start: end]

def collect_document_content(doc) -> List[Dict[str, Any]]:
    """收集文档中所有需要翻译的内容，但不立即翻译"""
    content_items = []

    # 收集页眉页脚
    for section in doc.sections:
        # 处理页眉
        if not section.header.is_linked_to_previous:
            for para in section.header.paragraphs:
                if para.text.strip() and not is_numeric_or_currency(para.text):
                    content_items.append({
                        'type': 'header',
                        'element': para,
                        'text': para.text.strip(),
                        'paragraph_style': DocxStyleManager.preserve_paragraph_style(para),
                        'run_styles': [DocxStyleManager.preserve_complete_run_style(run) for run in para.runs]
                    })

        # 处理页脚
        if not section.footer.is_linked_to_previous:
            for para in section.footer.paragraphs:
                if para.text.strip() and not is_numeric_or_currency(para.text):
                    content_items.append({
                        'type': 'footer',
                        'element': para,
                        'text': para.text.strip(),
                        'paragraph_style': DocxStyleManager.preserve_paragraph_style(para),
                        'run_styles': [DocxStyleManager.preserve_complete_run_style(run) for run in para.runs]
                    })

    # 收集主体段落
    for para in doc.paragraphs:
        if para.text.strip() and not is_numeric_or_currency(para.text):
            content_items.append({
                'type': 'paragraph',
                'element': para,
                'text': para.text.strip(),
                'paragraph_style': DocxStyleManager.preserve_paragraph_style(para),
                'run_styles': [DocxStyleManager.preserve_complete_run_style(run) for run in para.runs]
            })

    # 收集表格内容，同时保留表格结构
    for table_idx, table in enumerate(doc.tables):
        # 保留表格结构信息
        table_structure = TableStyleManager.preserve_table_structure(table)

        for row_idx, row in enumerate(table.rows):
            for cell_idx, cell in enumerate(row.cells):
                if not cell.paragraphs:
                    continue
                text = cell.text.strip()
                if text and not is_numeric_or_currency(text) and "Total" not in text:
                    # 保留完整的单元格信息
                    cell_data = TableStyleManager.preserve_cell_content_and_style(cell)

                    content_items.append({
                        'type': 'table_cell',
                        'element': cell,
                        'text': text,
                        'table_structure': table_structure,
                        'table_index': table_idx,
                        'row_index': row_idx,
                        'cell_index': cell_idx,
                        'cell_data': cell_data,
                        # 保持向后兼容
                        'paragraph_style': DocxStyleManager.preserve_paragraph_style(cell.paragraphs[0]),
                        'run_styles': [DocxStyleManager.preserve_complete_run_style(run)
                                     for run in cell.paragraphs[0].runs]
                    })

    return content_items


def apply_translation_to_content(content_item: Dict[str, Any], translated_text: str):
    """将翻译结果应用到内容项"""
    element = content_item['element']
    content_type = content_item['type']

    try:
        if content_type in ['header', 'footer', 'paragraph']:
            # 处理段落类型
            if len(element.runs) < 1:
                return

            # 清空并重建段落
            element.clear()
            DocxStyleManager.apply_paragraph_style(element, content_item['paragraph_style'])

            # 应用样式
            if content_item['run_styles']:
                new_run = element.add_run(translated_text)
                main_style = content_item['run_styles'][0]
                for style in content_item['run_styles']:
                    if style['text'].strip():
                        main_style = style
                        break
                DocxStyleManager.apply_run_style(new_run, main_style)

        elif content_type == 'table_cell':
            # 处理表格单元格，使用新的表格样式管理器
            try:
                # 使用完整的单元格数据恢复
                if 'cell_data' in content_item:
                    TableStyleManager.apply_cell_content_and_style(
                        element, content_item['cell_data'], translated_text
                    )
                else:
                    # 降级到原有逻辑（向后兼容）
                    element.text = ""
                    paragraph = element.paragraphs[0]

                    # 恢复段落样式
                    DocxStyleManager.apply_paragraph_style(paragraph, content_item['paragraph_style'])

                    # 添加翻译文本并应用样式
                    if content_item['run_styles']:
                        run = paragraph.add_run(translated_text)
                        main_style = content_item['run_styles'][0]
                        for style in content_item['run_styles']:
                            if style['text'].strip():
                                main_style = style
                                break
                        DocxStyleManager.apply_run_style(run, main_style)
                    else:
                        paragraph.add_run(translated_text)

                # 记录表格结构信息，稍后统一恢复
                if 'table_structure' in content_item and 'table_index' in content_item:
                    # 这里我们需要一个全局的表格结构缓存
                    if not hasattr(apply_translation_to_content, '_table_structures'):
                        apply_translation_to_content._table_structures = {}

                    table_idx = content_item['table_index']
                    apply_translation_to_content._table_structures[table_idx] = content_item['table_structure']

            except Exception as cell_error:
                log.error(f"应用表格单元格翻译失败: {str(cell_error)}")
                # 最终降级：直接设置文本
                try:
                    element.text = translated_text
                except Exception:
                    log.error("最终降级处理也失败")

    except Exception as e:
        log.error(f"应用翻译到 {content_type} 时出错: {str(e)}")


def translate_docx_memory_efficient(input_file: str, output_file: str, chunk_size: int = 50, memory_limit_mb: int = 500):
    """内存高效的DOCX翻译函数"""
    # log.info(f"开始内存高效翻译: {input_file} -> {output_file}")

    with memory_efficient_processing():
        try:
            # 加载文档
            doc = docx.Document(input_file)
            # log.info(f"文档加载完成，开始收集内容")

            # 收集所有需要翻译的内容
            content_items = collect_document_content(doc)
            total_items = len(content_items)
            # log.info(f"收集到 {total_items} 个需要翻译的内容项")

            if total_items == 0:
                # log.info("没有找到需要翻译的内容")
                doc.save(output_file)
                return

            # 创建流式处理器
            processor = StreamingDocumentProcessor(chunk_size, memory_limit_mb)

            # 分块处理
            processed_count = 0
            for chunk_info in processor.process_content_chunks(content_items):
                chunk_items = chunk_info['items']
                chunk_texts = [item['text'] for item in chunk_items]

                # log.info(f"处理块 {chunk_info['chunk_index'] + 1}/{chunk_info['total_chunks']}, "
                #         f"包含 {len(chunk_texts)} 个文本")

                # 翻译当前块
                translated_texts = processor.translate_chunk_with_cache(chunk_texts)

                # 应用翻译结果
                for item, translated_text in zip(chunk_items, translated_texts):
                    apply_translation_to_content(item, translated_text)
                    processed_count += 1

                # 块处理完成后清理内存
                processor.memory_manager.cleanup_memory()

                # log.debug(f"已处理 {processed_count}/{total_items} 个内容项")

            # 恢复所有表格的结构信息
            if hasattr(apply_translation_to_content, '_table_structures'):
                # log.info("恢复表格结构信息...")
                table_structures = apply_translation_to_content._table_structures

                for table_idx, table_structure in table_structures.items():
                    if table_idx < len(doc.tables):
                        try:
                            TableStyleManager.apply_table_structure(doc.tables[table_idx], table_structure)
                            log.debug(f"恢复表格 {table_idx + 1} 结构成功")
                        except Exception as e:
                            log.error(f"恢复表格 {table_idx + 1} 结构失败: {str(e)}")

                # 清理缓存
                delattr(apply_translation_to_content, '_table_structures')
                # log.info(f"已恢复 {len(table_structures)} 个表格的结构")

            # 保存文档
            # log.info("保存翻译后的文档...")
            doc.save(output_file)
            # log.info(f"翻译完成，共处理 {processed_count} 个内容项")

        except Exception as e:
            log.error(f"翻译过程中出错: {str(e)}")
            # 清理可能创建的输出文件
            if os.path.exists(output_file):
                try:
                    os.remove(output_file)
                except:
                    pass
            raise


def translate_docx(input_file: str, output_file: str):
    """智能DOCX翻译函数，根据文档大小选择处理方式"""
    try:
        # 首先检查文件大小
        file_size_mb = os.path.getsize(input_file) / 1024 / 1024
        # log.info(f"文档大小: {file_size_mb:.2f} MB")

        # 对于大文档（>10MB）或包含大量内容的文档，使用内存高效模式
        if file_size_mb > 10:
            # log.info("检测到大文档，使用内存高效模式")
            return translate_docx_memory_efficient(input_file, output_file, chunk_size=30, memory_limit_mb=300)

        # 对于中等文档（>5MB），使用较小的块大小
        elif file_size_mb > 5:
            # log.info("检测到中等大小文档，使用优化模式")
            return translate_docx_memory_efficient(input_file, output_file, chunk_size=50, memory_limit_mb=500)

        # 对于小文档，使用传统模式但加入内存管理
        # log.info("使用传统模式处理小文档")

        with memory_efficient_processing():
            doc = docx.Document(input_file)
            memory_manager = MemoryManager(max_memory_mb=200)

    except Exception as e:
        log.error(f"打开文件失败: {str(e)}")
        raise

    # 传统处理模式（保持向后兼容）
    all_paras = doc.paragraphs
    all_tables = doc.tables
    for section in doc.sections:
        # 处理页眉
        if section.header.is_linked_to_previous:
            continue

        header_texts = []
        header_paras = []
        for para in section.header.paragraphs:
            if para.text.strip() and not is_numeric_or_currency(para.text):
                header_texts.append(para.text.strip())
                header_paras.append(para)

        if header_texts:
            try:
                translated_texts = openaiTranslate.translate(header_texts)
                for para, text_target in zip(header_paras, translated_texts):
                    if len(para.runs) < 1:
                        continue

                    # 使用新的样式管理器保存完整样式
                    para_style = DocxStyleManager.preserve_paragraph_style(para)
                    run_styles = []
                    for run in para.runs:
                        run_styles.append(DocxStyleManager.preserve_complete_run_style(run))

                    # 清空段落并重建
                    para.clear()
                    DocxStyleManager.apply_paragraph_style(para, para_style)

                    # 重建runs，尽可能保持原有的格式分布
                    if run_styles:
                        # 如果只有一个run或翻译后文本较短，使用第一个run的样式
                        if len(run_styles) == 1 or len(text_target) <= 50:
                            new_run = para.add_run(text_target)
                            DocxStyleManager.apply_run_style(new_run, run_styles[0])
                        else:
                            # 对于复杂格式，尝试智能分配样式
                            # 这里简化处理，使用主要样式
                            new_run = para.add_run(text_target)
                            DocxStyleManager.apply_run_style(new_run, run_styles[0])

            except Exception as e:
                log.error(f'翻译页眉时出错: {str(e)}')

        # 处理页脚
        if section.footer.is_linked_to_previous:
            continue

        footer_texts = []
        footer_paras = []
        for para in section.footer.paragraphs:
            if para.text.strip() and not is_numeric_or_currency(para.text):
                footer_texts.append(para.text.strip())
                footer_paras.append(para)

        if footer_texts:
            try:
                translated_texts = openaiTranslate.translate(footer_texts)
                for para, text_target in zip(footer_paras, translated_texts):
                    if len(para.runs) < 1:
                        continue

                    # 使用新的样式管理器保存完整样式
                    para_style = DocxStyleManager.preserve_paragraph_style(para)
                    run_styles = []
                    for run in para.runs:
                        run_styles.append(DocxStyleManager.preserve_complete_run_style(run))

                    # 清空段落并重建
                    para.clear()
                    DocxStyleManager.apply_paragraph_style(para, para_style)

                    # 重建runs
                    if run_styles:
                        new_run = para.add_run(text_target)
                        DocxStyleManager.apply_run_style(new_run, run_styles[0])

            except Exception as e:
                log.error(f'翻译页脚时出错: {str(e)}')

    # 处理主体段落，添加内存管理
    batch_count = 0
    for para_batch in paras_generator(all_paras, batch_size=2):
        try:
            texts_to_translate = [para.text.strip() for para in para_batch]
            para_translated = openaiTranslate.translate(texts_to_translate)
        except Exception as e:
            log.error('翻译失败')
            raise e
        else:
            for para, text_target in zip(para_batch, para_translated):
                if len(para.runs) < 1:
                    continue

                # 使用新的样式管理器保存完整样式
                para_style = DocxStyleManager.preserve_paragraph_style(para)
                run_styles = []
                for run in para.runs:
                    run_styles.append(DocxStyleManager.preserve_complete_run_style(run))

                # 清空段落并重建
                para.clear()
                DocxStyleManager.apply_paragraph_style(para, para_style)

                # 智能重建runs，保持格式
                if run_styles:
                    if len(run_styles) == 1:
                        # 单一格式，直接应用
                        new_run = para.add_run(text_target)
                        DocxStyleManager.apply_run_style(new_run, run_styles[0])
                    else:
                        # 多种格式，尝试保持主要格式
                        new_run = para.add_run(text_target)
                        # 使用最常见的格式或第一个非空格式
                        main_style = run_styles[0]
                        for style in run_styles:
                            if style['text'].strip():  # 找到第一个非空格式
                                main_style = style
                                break
                        DocxStyleManager.apply_run_style(new_run, main_style)

            # 每处理几个批次后检查内存
            batch_count += 1
            if batch_count % 10 == 0:
                memory_manager.force_cleanup_if_needed()



    # 处理表格，添加内存管理和完整的表格结构保留
    table_count = 0
    for table in all_tables:
        log.debug(f"开始处理表格 {table_count + 1}")

        # 保留表格的完整结构信息
        table_structure = TableStyleManager.preserve_table_structure(table)

        table_texts = []
        cell_data_list = []

        for row_idx, row in enumerate(table.rows):
            for cell_idx, cell in enumerate(row.cells):
                if not cell.paragraphs:  # 添加空值检查
                    continue
                text = cell.text.strip()
                if text and not is_numeric_or_currency(text) and "Total" not in text:
                    table_texts.append(text)

                    # 保存单元格的完整内容和样式信息
                    cell_data = TableStyleManager.preserve_cell_content_and_style(cell)
                    cell_data_list.append((cell, cell_data, row_idx, cell_idx))

        # 批量翻译
        if table_texts:
            try:
                log.debug(f"翻译表格 {table_count + 1}，包含 {len(table_texts)} 个单元格")
                translated_texts = openaiTranslate.translate(table_texts)

                # 更新单元格内容和样式，保持表格结构
                for (cell, cell_data, row_idx, cell_idx), translated_text in zip(cell_data_list, translated_texts):
                    try:
                        # 使用表格样式管理器应用翻译内容
                        TableStyleManager.apply_cell_content_and_style(cell, cell_data, translated_text)
                        log.debug(f"成功更新单元格 [{row_idx}, {cell_idx}]")
                    except Exception as cell_error:
                        log.error(f"更新单元格 [{row_idx}, {cell_idx}] 失败: {str(cell_error)}")
                        # 降级处理：直接设置文本
                        try:
                            cell.text = translated_text
                        except Exception:
                            log.error(f"降级处理也失败，跳过单元格 [{row_idx}, {cell_idx}]")

                # 恢复表格结构（这是关键步骤！）
                log.debug(f"恢复表格 {table_count + 1} 的结构信息")
                TableStyleManager.apply_table_structure(table, table_structure)

                # log.info(f"表格 {table_count + 1} 翻译完成，保留了原始结构")

            except Exception as e:
                log.error(f'翻译表格内容时出错: {str(e)}')
                # 即使翻译失败，也尝试恢复表格结构
                try:
                    TableStyleManager.apply_table_structure(table, table_structure)
                    # log.info(f"表格 {table_count + 1} 结构已恢复（翻译失败）")
                except Exception as structure_error:
                    log.error(f"恢复表格结构也失败: {str(structure_error)}")

        table_count += 1
        # 每处理几个表格后检查内存
        if table_count % 5 == 0:
            memory_manager.force_cleanup_if_needed()

    # 最终保存和清理
    try:
        # log.info("保存翻译后的文档...")
        doc.save(output_file)
        # log.info("文档保存成功")

        # 最终内存清理
        memory_manager.cleanup_memory()

    except Exception as e:
        log.error(f"保存文件失败: {str(e)}")
        if os.path.exists(output_file):
            try:
                os.remove(output_file)
            except:
                pass
        raise
