import os
import pandas as pd
import xlrd
from xlutils.copy import copy
from openpyxl.reader.excel import load_workbook
from openpyxl.styles import Font, PatternFill, Border, Side, Alignment, Color, Protection
from openpyxl.styles.numbers import FORMAT_GENERAL
from typing import Dict, Any, Optional, Union
from core.log import setup_logger
from utils.translation.docx import is_numeric_or_currency
from utils.translation.translate import openaiTranslate
log = setup_logger(__name__)


class ExcelStyleManager:
    """Excel样式管理器，统一处理所有Excel格式的样式保留和恢复"""

    @staticmethod
    def capture_openpyxl_cell_style(cell) -> Dict[str, Any]:
        """捕获openpyxl单元格的完整样式（用于.xlsx文件）"""
        try:
            style_dict = {
                'font': {
                    'name': cell.font.name,
                    'size': cell.font.size,
                    'bold': cell.font.bold,
                    'italic': cell.font.italic,
                    'color': None,
                    'underline': cell.font.underline,
                    'strike': cell.font.strike,
                    'vertAlign': cell.font.vertAlign,
                },
                'alignment': {
                    'horizontal': cell.alignment.horizontal,
                    'vertical': cell.alignment.vertical,
                    'wrap_text': cell.alignment.wrap_text,
                    'shrink_to_fit': cell.alignment.shrink_to_fit,
                    'indent': cell.alignment.indent,
                    'text_rotation': cell.alignment.text_rotation,
                },
                'border': {
                    'left': None,
                    'right': None,
                    'top': None,
                    'bottom': None,
                    'diagonal': None,
                    'diagonal_direction': None,
                },
                'fill': {
                    'fill_type': cell.fill.fill_type,
                    'start_color': None,
                    'end_color': None,
                    'gradient_type': None,
                },
                'number_format': cell.number_format,
                'protection': {
                    'locked': cell.protection.locked,
                    'hidden': cell.protection.hidden,
                }
            }

            # 安全获取字体颜色
            if cell.font.color and hasattr(cell.font.color, 'rgb'):
                style_dict['font']['color'] = cell.font.color.rgb
            elif cell.font.color and hasattr(cell.font.color, 'indexed'):
                style_dict['font']['color'] = cell.font.color.indexed

            # 安全获取边框信息
            if cell.border:
                border_sides = ['left', 'right', 'top', 'bottom', 'diagonal']
                for side in border_sides:
                    border_side = getattr(cell.border, side, None)
                    if border_side:
                        style_dict['border'][side] = {
                            'style': border_side.style,
                            'color': border_side.color.rgb if border_side.color and hasattr(border_side.color, 'rgb') else None
                        }
                style_dict['border']['diagonal_direction'] = cell.border.diagonal_direction

            # 安全获取填充信息
            if cell.fill:
                if hasattr(cell.fill, 'start_color') and cell.fill.start_color:
                    if hasattr(cell.fill.start_color, 'rgb'):
                        style_dict['fill']['start_color'] = cell.fill.start_color.rgb
                    elif hasattr(cell.fill.start_color, 'indexed'):
                        style_dict['fill']['start_color'] = cell.fill.start_color.indexed

                if hasattr(cell.fill, 'end_color') and cell.fill.end_color:
                    if hasattr(cell.fill.end_color, 'rgb'):
                        style_dict['fill']['end_color'] = cell.fill.end_color.rgb
                    elif hasattr(cell.fill.end_color, 'indexed'):
                        style_dict['fill']['end_color'] = cell.fill.end_color.indexed

                if hasattr(cell.fill, 'gradient_type'):
                    style_dict['fill']['gradient_type'] = cell.fill.gradient_type

            return style_dict

        except Exception as e:
            log.warning(f"捕获单元格样式时出错: {str(e)}")
            return ExcelStyleManager._get_default_style()

    @staticmethod
    def apply_openpyxl_cell_style(cell, style_dict: Dict[str, Any]):
        """应用样式到openpyxl单元格（用于.xlsx文件）"""
        try:
            # 应用字体样式
            font_style = style_dict.get('font', {})
            font_kwargs = {}

            for attr in ['name', 'size', 'bold', 'italic', 'underline', 'strike', 'vertAlign']:
                if attr in font_style and font_style[attr] is not None:
                    font_kwargs[attr] = font_style[attr]

            if font_style.get('color'):
                font_kwargs['color'] = font_style['color']

            if font_kwargs:
                cell.font = Font(**font_kwargs)

            # 应用对齐样式
            alignment_style = style_dict.get('alignment', {})
            alignment_kwargs = {}

            for attr in ['horizontal', 'vertical', 'wrap_text', 'shrink_to_fit', 'indent', 'text_rotation']:
                if attr in alignment_style and alignment_style[attr] is not None:
                    alignment_kwargs[attr] = alignment_style[attr]

            if alignment_kwargs:
                cell.alignment = Alignment(**alignment_kwargs)

            # 应用边框样式
            border_style = style_dict.get('border', {})
            if any(border_style.get(side) for side in ['left', 'right', 'top', 'bottom']):
                border_kwargs = {}
                for side in ['left', 'right', 'top', 'bottom']:
                    side_info = border_style.get(side)
                    if side_info and isinstance(side_info, dict):
                        side_kwargs = {'style': side_info.get('style')}
                        if side_info.get('color'):
                            side_kwargs['color'] = side_info['color']
                        border_kwargs[side] = Side(**side_kwargs)

                if border_kwargs:
                    cell.border = Border(**border_kwargs)

            # 应用填充样式
            fill_style = style_dict.get('fill', {})
            if fill_style.get('start_color') or fill_style.get('fill_type'):
                fill_kwargs = {}
                if fill_style.get('fill_type'):
                    fill_kwargs['fill_type'] = fill_style['fill_type']
                if fill_style.get('start_color'):
                    fill_kwargs['start_color'] = fill_style['start_color']
                if fill_style.get('end_color'):
                    fill_kwargs['end_color'] = fill_style['end_color']

                if fill_kwargs:
                    cell.fill = PatternFill(**fill_kwargs)

            # 应用数字格式
            if style_dict.get('number_format'):
                cell.number_format = style_dict['number_format']

            # 应用保护设置
            protection_style = style_dict.get('protection', {})
            if protection_style:
                protection_kwargs = {}
                for attr in ['locked', 'hidden']:
                    if attr in protection_style and protection_style[attr] is not None:
                        protection_kwargs[attr] = protection_style[attr]

                if protection_kwargs:
                    cell.protection = Protection(**protection_kwargs)

        except Exception as e:
            log.warning(f"应用单元格样式时出错: {str(e)}")

    @staticmethod
    def _get_default_style() -> Dict[str, Any]:
        """获取默认样式"""
        return {
            'font': {
                'name': 'Calibri',
                'size': 11,
                'bold': False,
                'italic': False,
                'color': None,
                'underline': 'none',
                'strike': False,
                'vertAlign': None,
            },
            'alignment': {
                'horizontal': 'general',
                'vertical': 'bottom',
                'wrap_text': False,
                'shrink_to_fit': False,
                'indent': 0,
                'text_rotation': 0,
            },
            'border': {
                'left': None,
                'right': None,
                'top': None,
                'bottom': None,
            },
            'fill': {
                'fill_type': None,
                'start_color': None,
                'end_color': None,
            },
            'number_format': FORMAT_GENERAL,
            'protection': {
                'locked': True,
                'hidden': False,
            }
        }


def collect_texts_for_translation_with_styles(sheet):
    """收集需要翻译的文本、单元格位置和样式信息"""
    texts = []
    cell_info = []

    for row in range(1, sheet.max_row + 1):
        for col in range(1, sheet.max_column + 1):
            cell = sheet.cell(row=row, column=col)
            if cell.value and isinstance(cell.value, str) and not is_numeric_or_currency(cell.value):
                texts.append(cell.value.strip())
                # 保存单元格和其完整样式
                cell_style = ExcelStyleManager.capture_openpyxl_cell_style(cell)
                cell_info.append({
                    'cell': cell,
                    'style': cell_style,
                    'original_value': cell.value
                })

    return texts, cell_info


def collect_texts_for_translation(sheet):
    """收集需要翻译的文本和对应的单元格位置（向后兼容）"""
    texts = []
    cells = []
    for row in range(1, sheet.max_row + 1):
        for col in range(1, sheet.max_column + 1):
            cell = sheet.cell(row=row, column=col)
            if cell.value and isinstance(cell.value, str) and not is_numeric_or_currency(cell.value):
                texts.append(cell.value.strip())
                cells.append(cell)
    return texts, cells


def process_xlsx_file_unified(input_file: str, output_file: str):
    """统一处理.xlsx文件的翻译，使用新的样式管理器"""
    try:
        wb = load_workbook(input_file, data_only=False, keep_vba=False)
        # log.info(f"成功加载Excel文件: {input_file}")
    except Exception as e:
        log.error(f"打开Excel文件失败: {str(e)}")
        raise

    has_translation = False

    for sheet in wb.worksheets:
        # log.info(f'正在处理工作表: {sheet.title}')

        # 使用新的样式管理器收集文本和样式
        texts, cell_info = collect_texts_for_translation_with_styles(sheet)

        if not texts:
            log.info(f'工作表 {sheet.title} 没有需要翻译的内容')
            continue

        has_translation = True
        # log.info(f'工作表 {sheet.title} 收集到 {len(texts)} 个需要翻译的单元格')

        # 批量翻译
        try:
            translated_texts = openaiTranslate.translate(texts)
            # log.info(f'翻译完成，开始更新单元格...')

            # 更新单元格内容和样式
            for cell_data, translated_text in zip(cell_info, translated_texts):
                cell = cell_data['cell']
                original_style = cell_data['style']

                # 更新单元格值
                cell.value = translated_text

                # 恢复原始样式
                ExcelStyleManager.apply_openpyxl_cell_style(cell, original_style)

            # log.info(f'工作表 {sheet.title} 处理完成')

        except Exception as e:
            log.error(f'翻译工作表 {sheet.title} 时出错: {str(e)}')
            raise

    if not has_translation:
        log.info('没有找到需要翻译的内容')
        wb.close()
        return input_file

    # 保存文件
    try:
        wb.save(output_file)
        wb.close()
        # log.info(f'Excel文件翻译完成: {output_file}')
    except Exception as e:
        log.error(f"保存Excel文件失败: {str(e)}")
        if os.path.exists(output_file):
            try:
                os.remove(output_file)
            except:
                pass
        raise


def process_csv_file_unified(input_file: str, output_file: str):
    """统一处理CSV文件的翻译"""
    # log.info(f"开始处理CSV文件: {input_file}")

    # 尝试不同编码读取 CSV
    try:
        df = pd.read_csv(input_file)
    except UnicodeDecodeError:
        try:
            df = pd.read_csv(input_file, encoding='gbk')
        except UnicodeDecodeError:
            df = pd.read_csv(input_file, encoding='latin-1')

    # 收集需要翻译的文本
    texts = []
    positions = []

    # 处理表头
    for col in range(len(df.columns)):
        header = str(df.columns[col])
        if not is_numeric_or_currency(header):
            texts.append(header)
            positions.append(('header', col))

    # 处理内容
    for row in range(len(df)):
        for col in range(len(df.columns)):
            value = str(df.iloc[row, col])
            if value and value != 'nan' and not is_numeric_or_currency(value):
                texts.append(value)
                positions.append((row, col))

    if not texts:
        log.info('CSV文件没有需要翻译的内容')
        return input_file

    # log.info(f'CSV文件收集到 {len(texts)} 个需要翻译的文本')

    # 批量翻译
    try:
        translated_texts = openaiTranslate.translate(texts)

        # 更新翻译结果
        for (pos, translated_text) in zip(positions, translated_texts):
            if pos[0] == 'header':
                df.columns.values[pos[1]] = translated_text
            else:
                df.iloc[pos[0], pos[1]] = translated_text

        # 保存文件
        df.to_csv(output_file, index=False, encoding='utf-8-sig')
        # log.info(f'CSV文件翻译完成: {output_file}')

    except Exception as e:
        log.error(f"翻译CSV文件失败: {str(e)}")
        if os.path.exists(output_file):
            try:
                os.remove(output_file)
            except:
                pass
        raise


def excel_translate(input_file: str, output_file: str):
    """统一的Excel文件翻译入口函数"""
    if not os.path.exists(input_file):
        raise Exception(f"找不到文件: {input_file}")

    file_ext = os.path.splitext(input_file)[1].lower()
    # log.info(f"开始翻译Excel文件: {input_file} (格式: {file_ext})")

    # 根据文件类型选择处理方式
    if file_ext == '.csv':
        return process_csv_file_unified(input_file, output_file)
    elif file_ext in ['.xlsx', '.xlsm']:
        return process_xlsx_file_unified(input_file, output_file)
    elif file_ext == '.xls':
        return process_xls_file_unified(input_file, output_file)
    else:
        raise ValueError(f"不支持的Excel文件格式: {file_ext}")


def process_xls_file_unified(input_file: str, output_file: str):
    """统一处理.xls文件的翻译"""
    # log.info(f"开始处理XLS文件: {input_file}")

    try:
        workbook = xlrd.open_workbook(input_file)
        has_translation = False

        # 创建一个新的 xls 工作簿
        new_workbook = xlrd.open_workbook(input_file, formatting_info=True)
        new_workbook_copy = copy(new_workbook)

        for sheet_index in range(workbook.nsheets):
            sheet = workbook.sheet_by_index(sheet_index)
            sheet_name = sheet.name
            new_sheet = new_workbook_copy.get_sheet(sheet_index)

            # log.info(f'正在处理XLS工作表: {sheet_name}')

            # 收集需要翻译的文本
            texts = []
            positions = []

            for row in range(sheet.nrows):
                for col in range(sheet.ncols):
                    cell_value = sheet.cell_value(row, col)
                    if cell_value and isinstance(cell_value, str) and not is_numeric_or_currency(cell_value):
                        texts.append(cell_value.strip())
                        positions.append((row, col))

            if not texts:
                log.info(f'工作表 {sheet_name} 没有需要翻译的内容')
                continue

            has_translation = True
            # log.info(f'工作表 {sheet_name} 收集到 {len(texts)} 个需要翻译的单元格')

            try:
                translated_texts = openaiTranslate.translate(texts)

                # 更新翻译后的内容
                for (row, col), translated_text in zip(positions, translated_texts):
                    new_sheet.write(row, col, translated_text)

                # log.info(f'工作表 {sheet_name} 处理完成')

            except Exception as e:
                log.error(f'翻译工作表 {sheet_name} 时出错: {str(e)}')
                raise

        if not has_translation:
            log.info('XLS文件没有需要翻译的内容')
            return input_file

        # 保存文件
        try:
            new_workbook_copy.save(output_file)
            # log.info(f'XLS文件翻译完成: {output_file}')
        except Exception as e:
            log.error(f"保存XLS文件失败: {str(e)}")
            if os.path.exists(output_file):
                try:
                    os.remove(output_file)
                except:
                    pass
            raise

    except Exception as e:
        log.error(f"处理XLS文件失败: {str(e)}")
        raise
