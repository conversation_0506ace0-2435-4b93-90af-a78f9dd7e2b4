import os
import re
from typing import List
from dataclasses import dataclass
from bs4 import BeautifulSoup, NavigableString

from utils.translation.translate import  openaiTranslate

# 定义需要保留的特殊字符映射
SPECIAL_CHARS_MAP = {
    "§": True, ".": True, "-": True, "": True, "(+)": True,
    "®": True,  # 注册商标
    "™": True,  # 商标
    "©": True,  # 版权
    "$": True,  # 美元
    "€": True,  # 欧元
    "¥": True,  # 日元
    "£": True,  # 英镑
    "₹": True,  # 印度卢比
    "₩": True,  # 韩元
    "₽": True,  # 俄罗斯卢布
    "₫": True,  # 越南盾
    "฿": True,  # 泰铢
    "₴": True,  # 乌克兰格里夫纳
    "₦": True,  # 尼日利亚奈拉
    "₪": True,  # 以色列新谢克尔
    "₱": True,  # 菲律宾比索
    "₲": True,  # 巴拉圭瓜拉尼
    "₵": True,  # 加纳塞地
    "…": True,  # 省略号
    "•": True,  # 圆点符
    "«": True,  # 左尖括号
    "»": True,  # 右尖括号
    "±": True,  # 正负号
    "×": True,  # 乘号
    "÷": True,  # 除号
    "∑": True,  # 求和符号
    "∞": True,  # 无穷
    "≠": True,  # 不等号
    "√": True,  # 平方根
    "→": True,  # 右箭头
    "←": True,  # 左箭头
    "↑": True,  # 上箭头
    "↓": True,  # 下箭头
    "↔": True,  # 双向箭头
    "℃": True,  # 摄氏度
    "℉": True,  # 华氏度
    "°": True,  # 角度
    "☮": True,  # 和平符号
    "☯": True,  # 阴阳
    "☪": True,  # 伊斯兰新月
    "✝": True,  # 基督教十字架
    "✡": True,  # 犹太六芒星
    "℗": True,  # 唱片版权
    # 标点符号
    "'": True,  # 单引号
    "`": True,  # 反引号
    '"': True,  # 直引号
    "‚": True,  # 单底引号
    "„": True,  # 双底引号
    "‹": True,  # 单左角引号
    "›": True,  # 单右角引号
    "–": True,  # 短破折号
    "—": True,  # 长破折号
    "‒": True,  # 数字破折号
    "―": True,  # 水平线
    ";": True,  # 分号
    ":": True,  # 冒号
    "!": True,  # 感叹号
    "?": True,  # 问号
    "¿": True,  # 倒问号
    "¡": True,  # 倒感叹号
    # 数学符号
    "%": True,  # 百分号
    "‰": True,  # 千分号
    "‱": True,  # 万分号
    "½": True,  # 二分之一
    "¼": True,  # 四分之一
    "¾": True,  # 四分之三
    "⅓": True,  # 三分之一
    "⅔": True,  # 三分之二
    "⅕": True,  # 五分之一
    "⅖": True,  # 五分之二
    "⅗": True,  # 五分之三
    "⅘": True,  # 五分之四
    "⅙": True,  # 六分之一
    "⅚": True,  # 六分之五
    "⅛": True,  # 八分之一
    "⅜": True,  # 八分之三
    "⅝": True,  # 八分之五
    "⅞": True,  # 八分之七
    # 货币符号
    "₺": True,  # 土耳其里拉
    "₼": True,  # 阿塞拜疆马纳特
    "₸": True,  # 哈萨克斯坦坚戈
    "₾": True,  # 格鲁吉亚拉里
    "₿": True,  # 比特币
    # 其他符号
    "¶": True,  # 段落符号
    "†": True,  # 单剑号
    "‡": True,  # 双剑号
    "‣": True,  # 三角项目符号
    "⁃": True,  # 连字符项目符号
    "⁎": True,  # 小星号
    "⁂": True,  # 三星号
    "№": True,  # 编号符号
    "℠": True,  # 服务标记
    "℅": True,  # 收件人
    "℁": True,  # 账户
    "℮": True,  # 估计符号
    "⌘": True,  # 命令键
    "⌥": True,  # 选项键
    "⇧": True,  # 上档键
    "⌃": True,  # 控制键
    "⌫": True,  # 删除键
}


# 定义文本片段结构
@dataclass
class TextSegment:
    text: str  # 文本内容
    translatable: bool  # 是否可翻译


# 定义翻译任务结构
@dataclass
class TranslateTask:
    node_index: int  # 节点索引
    segments: List[TextSegment]  # 文本片段列表
    trailing_space: str  # 尾部空格


# 翻译选项配置
class TranslateOptions:
    def __init__(self, ignore_tags=None, ignore_classes=None, worker_count=5):
        self.ignore_tags = ignore_tags or []
        self.ignore_classes = ignore_classes or []
        self.worker_count = worker_count


class HTMLTranslator:
    def __init__(self):
        # self.translator = Translator()
        # self.translator =
        self.ignore_classes = []
        # 初始化忽略标签Map
        self.ignore_tags = {tag.lower(): True for tag in [
            "style", "script", "link", "code", "pre", "head"
        ]}

        # 定义特殊内容的正则表达式
        self.special_pattern = re.compile(
            r'(\S+@\S+\.\S+|https?://\S+|mailto:\S+|\+?\d+(-?\.?,?\d)+,?|'
            r'[a-zA-Z_][a-zA-Z0-9_]*=[^;]+;?|=\?.+\?=|[A-Z]([a-zA-Z]*)(-[a-zA-Z]+)+:|'
            r'[a-zA-z]==|\[|\]|\*+(:\*+)?|_+)'
        )

        # 邮箱地址正则表达式
        self.email_pattern = re.compile(r'<([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})>')

    def is_special_character(self, text):
        """判断文本是否包含特殊字符"""
        text = text.strip()
        for char in text:
            if char in SPECIAL_CHARS_MAP:
                return True
        return False

    def split_text(self, text):
        """将文本分割为可翻译和不可翻译的片段"""
        segments = []
        # 使用正则表达式查找所有特殊内容
        matches = list(self.special_pattern.finditer(text))

        if not matches:
            # 如果没有特殊内容，整个文本可翻译
            segments.append(TextSegment(
                text=text,
                translatable=True
            ))
            return segments

        # 处理特殊内容和普通文本
        last_end = 0
        for match in matches:
            start, end = match.span()

            # 添加特殊内容前的普通文本
            if start > last_end and text[last_end:start].strip():
                segments.append(TextSegment(
                    text=text[last_end:start].strip(),
                    translatable=True
                ))

            # 添加特殊内容
            segments.append(TextSegment(
                text=text[start:end],
                translatable=False
            ))

            last_end = end

        # 添加最后一个特殊内容后的普通文本
        if last_end < len(text) and text[last_end:].strip():
            segments.append(TextSegment(
                text=text[last_end:].strip(),
                translatable=True
            ))

        return segments

    def should_translate_node(self, node):
        """判断节点是否需要翻译"""
        # 检查标签名
        if hasattr(node, 'name') and node.name and node.name.lower() in self.ignore_tags:
            return False

        # 检查CSS类
        if hasattr(node, 'attrs') and 'class' in node.attrs:
            node_classes = ' '.join(node.attrs['class']) if isinstance(node.attrs['class'], list) else node.attrs[
                'class']
            for ignore_class in self.ignore_classes:
                if ignore_class in node_classes:
                    return False

        return True

    def collect_translatable_nodes(self, soup):
        """收集需要翻译的节点"""
        tasks = []
        nodes = []

        def process_node(node):
            if isinstance(node, NavigableString) and node.parent:
                text = str(node)
                original_space = ""

                # 保留原始空格
                if text.endswith(" "):
                    original_space = " "

                text = text.strip()
                if text and self.should_translate_node(node.parent):
                    segments = self.split_text(text)
                    if segments:
                        tasks.append(TranslateTask(
                            node_index=len(nodes),
                            segments=segments,
                            trailing_space=original_space
                        ))
                        nodes.append(node)
            elif node.name:
                for child in node.contents:
                    process_node(child)

        # 从body开始处理
        if soup.body:
            for child in soup.body.children:
                process_node(child)

        return tasks, nodes

    def text_translate(self, texts):
        """翻译文本列表"""
        if not texts:
            return []

        # 使用googletrans进行批量翻译
        translations = []

        # 由于googletrans有请求限制，分批翻译
        batch_size = 100
        for i in range(0, len(texts), batch_size):
            batch = texts[i:i + batch_size]
            try:
                # 批量翻译
                results = openaiTranslate.translate(batch)
                translations.extend(results)
            except Exception as e:
                print(f"翻译出错: {e}")
                # 出错时使用原文本
                translations.extend(batch)

        return translations

    def translate_concurrently(self, tasks):
        """并发翻译文本"""
        if not tasks:
            return None, None

        # 收集需要翻译的文本
        texts_to_translate = []
        text_indices = []

        for task_index, task in enumerate(tasks):
            for segment in task.segments:
                if segment.translatable:
                    texts_to_translate.append(segment.text)
                    text_indices.append(task_index)

        # 执行翻译
        translations = self.text_translate(texts_to_translate)

        # 重组翻译结果
        results = ["" for _ in range(len(tasks))]
        translation_index = 0

        for i, task in enumerate(tasks):
            parts = []
            for segment in task.segments:
                if segment.translatable:
                    parts.append(translations[translation_index])
                    translation_index += 1
                else:
                    parts.append(segment.text)
            results[i] = " ".join(parts)

        return results

    def escape_email_brackets(self, text):
        """将邮箱地址中的尖括号转换为HTML实体"""
        return self.email_pattern.sub(r"&lt;\1&gt;", text)

    def apply_translations(self, nodes, translations):
        """将翻译结果应用到HTML节点"""
        for i, node in enumerate(nodes):
            if i < len(translations):
                # 获取原始文本以检查空格
                original_text = str(node)
                translated_text = translations[i]

                # 如果原始文本以空格结尾，在翻译后的文本后添加空格
                if original_text.endswith(" ") and not translated_text.endswith(" "):
                    translated_text += " "

                # 处理邮箱地址中的尖括号，将其转换为HTML实体
                translated_text = self.escape_email_brackets(translated_text)

                # 替换节点内容
                new_node = NavigableString(translated_text)
                node.replace_with(new_node)

    def translate_html(self, input_file: str, output_file: str):
        """翻译HTML文档"""
        if not os.path.exists(input_file):
            raise Exception(f"找不到文件: {input_file}")

        with open(input_file, "r", encoding="utf-8") as f:
            html_content = f.read()
        # 解析HTML文档
        soup = BeautifulSoup(html_content, 'html.parser')

        # 收集需要翻译的文本
        tasks, nodes = self.collect_translatable_nodes(soup)
        if not tasks:
            with open(output_file, "w", encoding="utf-8") as f:
                f.write(str(soup))
            return

        # 翻译文本
        translations = self.translate_concurrently(tasks)

        # 将翻译结果应用到HTML
        self.apply_translations(nodes, translations)

        # 保存结果
        with open(output_file, "w", encoding="utf-8") as f:
            f.write(str(soup))
        # # 输出翻译后的HTML
        # return str(soup)