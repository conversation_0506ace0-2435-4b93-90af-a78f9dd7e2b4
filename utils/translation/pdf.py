import asyncio

from core.log import setup_logger
import os
import pdf2zh_next
from pdf2zh_next.high_level import do_translate_file, do_translate_async_stream

from core.setting import tool_settings

logger = setup_logger(__name__)

class Pdf2zhTranslator:
    def __init__(self, input_file: str, output_path: str):
        self.settings = pdf2zh_next.SettingsModel(
            basic=pdf2zh_next.BasicSettings(
                input_files={input_file}
            ),
            pdf=pdf2zh_next.PDFSettings(
                no_mono=True,
                watermark_output_mode=pdf2zh_next.WatermarkOutputMode.NoWatermark,
                skip_scanned_detection= True,
                ocr_workaround=True
            ),
            translation=pdf2zh_next.TranslationSettings(
                qps=tool_settings.tool_config.qps,
                no_auto_extract_glossary=True,
                custom_system_prompt=tool_settings.llm_config.translate_prompt,
                output=output_path,
            ),
            translate_engine_settings=pdf2zh_next.OpenAISettings(
                openai_api_key=tool_settings.llm_config.api_key,
                openai_base_url=tool_settings.llm_config.base_url,
                openai_model=tool_settings.llm_config.model
            )
        )
    def __call__(self, *args, **kwargs):
        try:
            return asyncio.run(self.translate_async())
        except KeyboardInterrupt:
            logger.info("Translation interrupted by user (Ctrl+C)")
            return 1  # Return error count = 1 to indicate interruption
        except RuntimeError as e:
            # Handle the case where run() is called from a running event loop
            if "asyncio.run() cannot be called from a running event loop" in str(e):
                loop = asyncio.get_event_loop()
                try:
                    return loop.run_until_complete(
                        self.translate_async()
                    )
                except KeyboardInterrupt:
                    logger.info("Translation interrupted by user (Ctrl+C) in event loop")
                    return 1  # Return error count = 1 to indicate interruption
            else:
                raise

    async def translate_async(self):
        input_files = self.settings.basic.input_files
        assert len(input_files) >= 1, "At least one input file is required"
        self.settings.basic.input_files = set()
        for file in input_files:
            async for event in do_translate_async_stream(self.settings, file):
                pass


