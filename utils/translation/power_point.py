import os
import math
from dataclasses import dataclass
from enum import Enum
from typing import Dict, List, Tuple, Optional, Any

from pptx import Presentation
from pptx.enum.text import MSO_AUTO_SIZE
from pptx.util import Inches, Pt
from core.log import setup_logger
from utils.translation.docx import is_numeric_or_currency
from utils.translation.translate import OpenaiTranslate, ErrorHandler, TransactionManager

log = setup_logger(__name__)


class OverflowStrategy(Enum):
    """文本溢出处理策略"""
    EXPAND_WIDTH = "expand_width"
    EXPAND_HEIGHT = "expand_height"
    EXPAND_BOTH = "expand_both"
    REDUCE_FONT = "reduce_font"
    ENABLE_WRAP = "enable_wrap"
    SPLIT_TEXT = "split_text"
    NO_ACTION = "no_action"


class TextBoxType(Enum):
    """文本框类型"""
    TITLE = "title"
    SUBTITLE = "subtitle"
    CONTENT = "content"
    CHART_TITLE = "chart_title"
    CHART_LABEL = "chart_label"
    SMART_ART = "smart_art"
    NOTES = "notes"
    OTHER = "other"


@dataclass
class FontMetrics:
    """字体度量信息"""
    font_name: str = "Calibri"
    font_size: float = 18.0  # 点数
    char_width_ratio: float = 0.6  # 字符宽度与字体大小的比例
    line_height_ratio: float = 1.2  # 行高与字体大小的比例
    chinese_width_ratio: float = 1.0  # 中文字符宽度比例
    english_width_ratio: float = 0.5  # 英文字符宽度比例


@dataclass
class TextBoxDimensions:
    """文本框尺寸信息"""
    width: int
    height: int
    max_width: int
    max_height: int
    min_width: int = 0
    min_height: int = 0


@dataclass
class TextAnalysis:
    """文本分析结果"""
    total_chars: int
    chinese_chars: int
    english_chars: int
    punctuation_chars: int
    line_count: int
    avg_line_length: float
    complexity_score: float
    estimated_width: float
    estimated_height: float


@dataclass
class AdjustmentResult:
    """调整结果"""
    success: bool
    original_width: int
    original_height: int
    new_width: int
    new_height: int
    strategy_used: OverflowStrategy
    font_size_changed: bool = False
    original_font_size: float = 0.0
    new_font_size: float = 0.0
    error_message: str = ""


class SmartTextBoxAdjuster:
    """智能文本框调整器"""

    def __init__(self):
        self.error_handler = ErrorHandler()
        self.font_metrics_cache = {}
        self.slide_dimensions = {
            'width': 9144000,  # 10英寸，单位EMU
            'height': 6858000,  # 7.5英寸，单位EMU
        }
        self.max_width_ratio = 0.9  # 最大宽度占幻灯片宽度的比例
        self.max_height_ratio = 0.8  # 最大高度占幻灯片高度的比例

    def get_font_metrics(self, font_name: str, font_size: float) -> FontMetrics:
        """获取字体度量信息"""
        cache_key = f"{font_name}_{font_size}"

        if cache_key not in self.font_metrics_cache:
            # 根据字体名称调整度量参数
            if font_name in ['Arial', 'Helvetica']:
                char_width_ratio = 0.55
                chinese_width_ratio = 1.0
                english_width_ratio = 0.5
            elif font_name in ['Times New Roman', 'Times']:
                char_width_ratio = 0.5
                chinese_width_ratio = 1.0
                english_width_ratio = 0.45
            elif font_name in ['Calibri', 'Segoe UI']:
                char_width_ratio = 0.6
                chinese_width_ratio = 1.0
                english_width_ratio = 0.5
            elif font_name in ['SimSun', 'Microsoft YaHei', '微软雅黑']:
                char_width_ratio = 0.7
                chinese_width_ratio = 1.0
                english_width_ratio = 0.6
            else:
                # 默认值
                char_width_ratio = 0.6
                chinese_width_ratio = 1.0
                english_width_ratio = 0.5

            self.font_metrics_cache[cache_key] = FontMetrics(
                font_name=font_name,
                font_size=font_size,
                char_width_ratio=char_width_ratio,
                chinese_width_ratio=chinese_width_ratio,
                english_width_ratio=english_width_ratio
            )

        return self.font_metrics_cache[cache_key]

    def analyze_text(self, text: str, font_metrics: FontMetrics) -> TextAnalysis:
        """分析文本特征"""
        if not text:
            return TextAnalysis(0, 0, 0, 0, 0, 0.0, 0.0, 0.0, 0.0)

        lines = text.split('\n')
        line_count = len(lines)

        # 字符统计
        chinese_chars = sum(1 for c in text if '\u4e00' <= c <= '\u9fff')
        english_chars = sum(1 for c in text if c.isalpha() and not ('\u4e00' <= c <= '\u9fff'))
        punctuation_chars = sum(1 for c in text if not c.isalnum() and not c.isspace())
        total_chars = len(text)

        # 平均行长度
        avg_line_length = sum(len(line) for line in lines) / line_count if line_count > 0 else 0

        # 复杂度评分（基于字符类型分布和行数）
        complexity_score = (
            (chinese_chars / total_chars) * 0.4 +
            (punctuation_chars / total_chars) * 0.3 +
            min(line_count / 5, 1.0) * 0.3
        ) if total_chars > 0 else 0

        # 估算尺寸
        font_size_emu = font_metrics.font_size * 12700  # 点转EMU

        # 计算每行的估算宽度
        max_line_width = 0
        for line in lines:
            line_chinese = sum(1 for c in line if '\u4e00' <= c <= '\u9fff')
            line_english = len(line) - line_chinese

            line_width = (
                line_chinese * font_size_emu * font_metrics.chinese_width_ratio +
                line_english * font_size_emu * font_metrics.english_width_ratio
            )
            max_line_width = max(max_line_width, line_width)

        estimated_width = max_line_width
        estimated_height = line_count * font_size_emu * font_metrics.line_height_ratio

        return TextAnalysis(
            total_chars=total_chars,
            chinese_chars=chinese_chars,
            english_chars=english_chars,
            punctuation_chars=punctuation_chars,
            line_count=line_count,
            avg_line_length=avg_line_length,
            complexity_score=complexity_score,
            estimated_width=estimated_width,
            estimated_height=estimated_height
        )

    def classify_textbox_type(self, shape, context: str = "") -> TextBoxType:
        """分类文本框类型"""
        if not hasattr(shape, 'text_frame'):
            return TextBoxType.OTHER

        text = shape.text_frame.text.lower().strip()

        # 根据上下文分类
        if context:
            context_lower = context.lower()
            if 'title' in context_lower:
                return TextBoxType.TITLE
            elif 'chart' in context_lower:
                return TextBoxType.CHART_TITLE if 'title' in context_lower else TextBoxType.CHART_LABEL
            elif 'smart_art' in context_lower:
                return TextBoxType.SMART_ART
            elif 'notes' in context_lower:
                return TextBoxType.NOTES

        # 根据文本特征分类
        if len(text) < 50 and '\n' not in text:
            if any(keyword in text for keyword in ['第', '章', 'chapter', 'title', '标题']):
                return TextBoxType.TITLE
            elif len(text) < 20:
                return TextBoxType.CHART_LABEL
            else:
                return TextBoxType.SUBTITLE
        elif len(text) > 100 or '\n' in text:
            return TextBoxType.CONTENT

        return TextBoxType.OTHER

    def detect_overflow(self, shape, text_analysis: TextAnalysis, font_metrics: FontMetrics) -> bool:
        """检测文本是否溢出"""
        if not hasattr(shape, 'text_frame'):
            return False

        current_width = shape.width
        current_height = shape.height

        # 考虑内边距
        padding_width = current_width * 0.1  # 10%内边距
        padding_height = current_height * 0.1

        available_width = current_width - padding_width
        available_height = current_height - padding_height

        # 检查是否溢出
        width_overflow = text_analysis.estimated_width > available_width
        height_overflow = text_analysis.estimated_height > available_height

        return width_overflow or height_overflow

    def determine_adjustment_strategy(self,
                                    textbox_type: TextBoxType,
                                    text_analysis: TextAnalysis,
                                    current_dimensions: TextBoxDimensions,
                                    overflow_detected: bool) -> OverflowStrategy:
        """确定调整策略"""
        if not overflow_detected:
            return OverflowStrategy.NO_ACTION

        # 根据文本框类型选择策略
        if textbox_type == TextBoxType.TITLE:
            # 标题优先扩展宽度，避免换行
            if text_analysis.line_count == 1:
                return OverflowStrategy.EXPAND_WIDTH
            else:
                return OverflowStrategy.REDUCE_FONT

        elif textbox_type == TextBoxType.CHART_LABEL:
            # 图表标签优先缩小字体
            return OverflowStrategy.REDUCE_FONT

        elif textbox_type == TextBoxType.CONTENT:
            # 内容文本优先启用换行
            if text_analysis.line_count == 1 and text_analysis.total_chars > 50:
                return OverflowStrategy.ENABLE_WRAP
            else:
                return OverflowStrategy.EXPAND_BOTH

        elif textbox_type == TextBoxType.SMART_ART:
            # SmartArt优先缩小字体，保持布局
            return OverflowStrategy.REDUCE_FONT

        else:
            # 默认策略：根据文本特征选择
            if text_analysis.line_count == 1:
                return OverflowStrategy.EXPAND_WIDTH
            else:
                return OverflowStrategy.EXPAND_BOTH

    def apply_adjustment_strategy(self,
                                shape,
                                strategy: OverflowStrategy,
                                text_analysis: TextAnalysis,
                                font_metrics: FontMetrics) -> AdjustmentResult:
        """应用调整策略"""
        original_width = shape.width
        original_height = shape.height

        result = AdjustmentResult(
            success=False,
            original_width=original_width,
            original_height=original_height,
            new_width=original_width,
            new_height=original_height,
            strategy_used=strategy
        )

        try:
            if strategy == OverflowStrategy.NO_ACTION:
                result.success = True
                return result

            elif strategy == OverflowStrategy.EXPAND_WIDTH:
                new_width = min(
                    int(text_analysis.estimated_width * 1.2),  # 20%缓冲
                    int(self.slide_dimensions['width'] * self.max_width_ratio)
                )
                shape.width = new_width
                result.new_width = new_width
                result.success = True

            elif strategy == OverflowStrategy.EXPAND_HEIGHT:
                new_height = min(
                    int(text_analysis.estimated_height * 1.2),
                    int(self.slide_dimensions['height'] * self.max_height_ratio)
                )
                shape.height = new_height
                result.new_height = new_height
                result.success = True

            elif strategy == OverflowStrategy.EXPAND_BOTH:
                new_width = min(
                    int(text_analysis.estimated_width * 1.2),
                    int(self.slide_dimensions['width'] * self.max_width_ratio)
                )
                new_height = min(
                    int(text_analysis.estimated_height * 1.2),
                    int(self.slide_dimensions['height'] * self.max_height_ratio)
                )
                shape.width = new_width
                shape.height = new_height
                result.new_width = new_width
                result.new_height = new_height
                result.success = True

            elif strategy == OverflowStrategy.REDUCE_FONT:
                if hasattr(shape, 'text_frame') and shape.text_frame.paragraphs:
                    original_font_size = font_metrics.font_size
                    # 逐步减小字体，直到适合或达到最小值
                    min_font_size = max(8, original_font_size * 0.6)  # 最小不低于8pt或原大小的60%

                    for reduction_factor in [0.9, 0.8, 0.7, 0.6]:
                        new_font_size = original_font_size * reduction_factor
                        if new_font_size < min_font_size:
                            break

                        # 应用新字体大小
                        for paragraph in shape.text_frame.paragraphs:
                            for run in paragraph.runs:
                                run.font.size = Pt(new_font_size)

                        # 重新分析文本
                        new_font_metrics = self.get_font_metrics(font_metrics.font_name, new_font_size)
                        new_analysis = self.analyze_text(shape.text_frame.text, new_font_metrics)

                        # 检查是否还溢出
                        if not self.detect_overflow(shape, new_analysis, new_font_metrics):
                            result.font_size_changed = True
                            result.original_font_size = original_font_size
                            result.new_font_size = new_font_size
                            result.success = True
                            break

            elif strategy == OverflowStrategy.ENABLE_WRAP:
                if hasattr(shape, 'text_frame'):
                    shape.text_frame.word_wrap = True
                    shape.text_frame.auto_size = MSO_AUTO_SIZE.SHAPE_TO_FIT_TEXT
                    result.success = True

            elif strategy == OverflowStrategy.SPLIT_TEXT:
                # 这个策略比较复杂，暂时标记为不成功
                result.success = False
                result.error_message = "文本分割策略暂未实现"

        except Exception as e:
            result.success = False
            result.error_message = str(e)
            log.error(f"应用调整策略失败: {str(e)}")

        return result

    def smart_adjust_textbox(self, shape, new_text: str, context: str = "") -> AdjustmentResult:
        """智能调整文本框"""
        try:
            # 分类文本框类型
            textbox_type = self.classify_textbox_type(shape, context)

            # 获取字体信息
            font_name = "Calibri"
            font_size = 18.0

            if hasattr(shape, 'text_frame') and shape.text_frame.paragraphs:
                first_paragraph = shape.text_frame.paragraphs[0]
                if first_paragraph.runs:
                    first_run = first_paragraph.runs[0]
                    if first_run.font.name:
                        font_name = first_run.font.name
                    if first_run.font.size:
                        font_size = first_run.font.size.pt

            font_metrics = self.get_font_metrics(font_name, font_size)

            # 分析新文本
            text_analysis = self.analyze_text(new_text, font_metrics)

            # 检测溢出
            overflow_detected = self.detect_overflow(shape, text_analysis, font_metrics)

            # 确定调整策略
            strategy = self.determine_adjustment_strategy(
                textbox_type, text_analysis,
                TextBoxDimensions(shape.width, shape.height,
                                int(self.slide_dimensions['width'] * self.max_width_ratio),
                                int(self.slide_dimensions['height'] * self.max_height_ratio)),
                overflow_detected
            )

            log.debug(f"文本框调整: 类型={textbox_type.value}, 策略={strategy.value}, "
                     f"溢出={overflow_detected}, 字符数={text_analysis.total_chars}")

            # 应用调整策略
            result = self.apply_adjustment_strategy(shape, strategy, text_analysis, font_metrics)

            return result

        except Exception as e:
            error_info = self.error_handler.handle_error(e, {
                'context': context,
                'text_length': len(new_text),
                'shape_type': type(shape).__name__
            })

            return AdjustmentResult(
                success=False,
                original_width=getattr(shape, 'width', 0),
                original_height=getattr(shape, 'height', 0),
                new_width=getattr(shape, 'width', 0),
                new_height=getattr(shape, 'height', 0),
                strategy_used=OverflowStrategy.NO_ACTION,
                error_message=error_info.message
            )


# 创建全局调整器实例
smart_adjuster = SmartTextBoxAdjuster()


def collect_texts_from_chart(chart):
    """从图表中收集文本"""
    texts = []
    elements = []

    # 收集图表标题
    if chart.has_title and chart.chart_title.text_frame.text.strip():
        title_text = chart.chart_title.text_frame.text.strip()
        if not is_numeric_or_currency(title_text):
            texts.append(title_text)
            elements.append(("chart_title", chart.chart_title.text_frame))

    # 收集坐标轴标题和标签
    for axis in (chart.value_axis, chart.category_axis):
        if axis and axis.has_title and axis.axis_title.text_frame.text.strip():
            axis_text = axis.axis_title.text_frame.text.strip()
            if not is_numeric_or_currency(axis_text):
                texts.append(axis_text)
                elements.append(("axis_title", axis.axis_title.text_frame))

    return texts, elements

def collect_texts_from_smart_art(smart_art):
    """从SmartArt中收集文本"""
    texts = []
    elements = []

    for node in smart_art.nodes:
        if node.text_frame.text.strip():
            text = node.text_frame.text.strip()
            if not is_numeric_or_currency(text):
                texts.append(text)
                elements.append(("smart_art", node.text_frame))

    return texts, elements


def collect_texts_from_shape(shape):
    """从形状中收集文本"""
    texts = []
    shapes = []

    if hasattr(shape, "text_frame") and shape.text_frame.text.strip():
        text = shape.text_frame.text.strip()
        if not is_numeric_or_currency(text):  # 只收集需要翻译的文本
            texts.append(text)
            shapes.append(shape)

    if hasattr(shape, "shapes"):
        for sub_shape in shape.shapes:
            sub_texts, sub_shapes = collect_texts_from_shape(sub_shape)
            texts.extend(sub_texts)
            shapes.extend(sub_shapes)

    return texts, shapes


def update_shape_text(shape, new_text, context: str = ""):
    """更新形状中的文本，使用智能调整"""
    transaction = TransactionManager()

    try:
        if hasattr(shape, "text_frame"):
            text_frame = shape.text_frame
            original_text = text_frame.text

            # 记录原始状态用于回滚
            original_width = shape.width
            original_height = shape.height
            original_word_wrap = text_frame.word_wrap
            original_auto_size = text_frame.auto_size

            def rollback_shape():
                """回滚形状到原始状态"""
                try:
                    shape.width = original_width
                    shape.height = original_height
                    text_frame.text = original_text
                    text_frame.word_wrap = original_word_wrap
                    text_frame.auto_size = original_auto_size
                except Exception as e:
                    log.error(f"回滚形状失败: {str(e)}")

            transaction.add_operation("更新文本框", rollback_shape)

            # 更新文本
            text_frame.text = new_text

            # 设置基本文本框格式
            text_frame.word_wrap = True
            text_frame.auto_size = MSO_AUTO_SIZE.SHAPE_TO_FIT_TEXT

            # 使用智能调整器
            adjustment_result = smart_adjuster.smart_adjust_textbox(shape, new_text, context)

            if adjustment_result.success:
                log.debug(f"文本框调整成功: {adjustment_result.strategy_used.value}, "
                         f"尺寸变化: {original_width}x{original_height} -> "
                         f"{adjustment_result.new_width}x{adjustment_result.new_height}")

                if adjustment_result.font_size_changed:
                    log.debug(f"字体大小调整: {adjustment_result.original_font_size} -> "
                             f"{adjustment_result.new_font_size}")

                # 提交事务
                transaction.commit()
            else:
                log.warning(f"文本框调整失败: {adjustment_result.error_message}")
                # 不回滚，保持基本的文本更新
                transaction.commit()

        # 如果形状是组合形状，递归处理
        if hasattr(shape, "shapes"):
            for sub_shape in shape.shapes:
                update_shape_text(sub_shape, new_text, context)

    except Exception as e:
        log.error(f"更新形状文本失败: {str(e)}")
        transaction.rollback()
        raise


def update_shape_text_enhanced(shape, new_text, context: str = "", enable_smart_adjust: bool = True):
    """增强版文本更新函数，支持禁用智能调整"""
    if enable_smart_adjust:
        update_shape_text(shape, new_text, context)
    else:
        # 使用原始的简单调整逻辑
        update_shape_text_legacy(shape, new_text)


def update_shape_text_legacy(shape, new_text):
    """原始的文本更新逻辑（向后兼容）"""
    if hasattr(shape, "text_frame"):
        text_frame = shape.text_frame
        original_text = text_frame.text

        # 保存原始尺寸
        original_width = shape.width
        original_height = shape.height

        # 更新文本
        text_frame.text = new_text

        # 设置文本框格式
        text_frame.word_wrap = True
        text_frame.auto_size = MSO_AUTO_SIZE.SHAPE_TO_FIT_TEXT

        # 根据文本长度和字符类型调整大小
        if len(new_text) > len(original_text):
            # 计算中英文字符比例
            chinese_ratio = sum(1 for c in new_text if '\u4e00' <= c <= '\u9fff') / len(new_text) if new_text else 0

            # 根据中英文比例调整宽度
            if chinese_ratio > 0.5:
                width_ratio = 1.3  # 中文占主导时的宽度比例
            else:
                width_ratio = 1.5  # 英文占主导时的宽度比例

            # 调整宽度，但不超过幻灯片宽度的90%
            new_width = min(int(original_width * width_ratio), int(9144000 * 0.9))
            shape.width = new_width

            # 如果高度过大，适当增加宽度减少高度
            if shape.height > original_height * 2:
                shape.width = min(int(new_width * 1.2), int(9144000 * 0.9))

    # 如果形状是组合形状，递归处理
    if hasattr(shape, "shapes"):
        for sub_shape in shape.shapes:
            update_shape_text_legacy(sub_shape, new_text)


def ppt_translate(input_file: str, output_file: str, use_smart_adjustment: bool = True):
    """
    翻译PowerPoint文件

    Args:
        input_file: 输入文件路径
        output_file: 输出文件路径
        use_smart_adjustment: 是否使用智能文本框调整
    """
    if not os.path.exists(input_file):
        raise Exception(f"找不到文件: {input_file}")

    # 初始化错误处理和事务管理
    error_handler = ErrorHandler()
    transaction = TransactionManager()

    try:
        # 加载PPT文件
        log.info(f"开始翻译PowerPoint文件: {input_file}")
        prs = Presentation(input_file)

        # 收集所有需要翻译的文本
        all_texts = []
        text_shapes = []
        context_hints = []  # 上下文提示，用于智能批处理

        log.info("收集需要翻译的文本...")

        # 处理母版
        for slide_master in prs.slide_masters:
            for shape in slide_master.shapes:
                texts, shapes = collect_texts_from_shape(shape)
                if texts:
                    all_texts.extend(texts)
                    text_shapes.extend([("master", shape) for shape in shapes])
                    context_hints.extend(["master"] * len(texts))

            # 处理母版布局
            for layout in slide_master.slide_layouts:
                for shape in layout.shapes:
                    texts, shapes = collect_texts_from_shape(shape)
                    if texts:
                        all_texts.extend(texts)
                        text_shapes.extend([("layout", shape) for shape in shapes])
                        context_hints.extend(["layout"] * len(texts))

        # 处理普通幻灯片
        for slide_idx, slide in enumerate(prs.slides):
            # 处理笔记
            if slide.has_notes_slide:
                notes_slide = slide.notes_slide
                notes_text = notes_slide.notes_text_frame.text.strip()
                if notes_text and not is_numeric_or_currency(notes_text):
                    all_texts.append(notes_text)
                    text_shapes.append(("notes", notes_slide.notes_text_frame))
                    context_hints.append("notes")

            for shape in slide.shapes:
                # 处理图表
                if shape.has_chart:
                    chart_texts, chart_elements = collect_texts_from_chart(shape.chart)
                    if chart_texts:
                        all_texts.extend(chart_texts)
                        text_shapes.extend(chart_elements)
                        context_hints.extend([elem[0] for elem in chart_elements])

                # 处理SmartArt
                elif hasattr(shape, 'graphic') and hasattr(shape.graphic, 'graphicData'):
                    if shape.graphic.graphicData.uri == 'http://schemas.openxmlformats.org/drawingml/2006/diagram':
                        smart_art_texts, smart_art_elements = collect_texts_from_smart_art(shape)
                        if smart_art_texts:
                            all_texts.extend(smart_art_texts)
                            text_shapes.extend(smart_art_elements)
                            context_hints.extend([elem[0] for elem in smart_art_elements])

                # 处理普通形状
                else:
                    texts, shapes = collect_texts_from_shape(shape)
                    if texts:
                        all_texts.extend(texts)
                        text_shapes.extend(shapes)
                        context_hints.extend(["shape"] * len(texts))

        if not all_texts:
            log.info("没有找到需要翻译的文本")
            return

        log.info(f"找到 {len(all_texts)} 个需要翻译的文本")

        # 使用智能批处理翻译
        log.info("开始智能批处理翻译...")
        translator = OpenaiTranslate()
        translated_texts = translator.translate(all_texts, use_smart_batch=True, context_hints=context_hints)

        # 更新PPT内容
        log.info("更新PPT内容...")
        successful_updates = 0
        failed_updates = 0
        adjustment_stats = {
            'total_adjustments': 0,
            'successful_adjustments': 0,
            'strategy_usage': {}
        }

        def rollback_presentation():
            """回滚演示文稿到原始状态"""
            try:
                # 重新加载原始文件
                prs = Presentation(input_file)
            except Exception as e:
                log.error(f"回滚演示文稿失败: {str(e)}")

        transaction.add_operation("更新演示文稿", rollback_presentation)

        for i, (shape, new_text) in enumerate(zip(text_shapes, translated_texts)):
            try:
                context = context_hints[i] if i < len(context_hints) else ""

                if isinstance(shape, tuple):
                    shape_type, element = shape
                    if shape_type == "notes":
                        element.text = str(new_text)
                        successful_updates += 1
                    elif shape_type in ("master", "layout"):
                        if use_smart_adjustment:
                            update_shape_text(element, str(new_text), shape_type)
                        else:
                            update_shape_text_legacy(element, str(new_text))
                        successful_updates += 1
                        adjustment_stats['total_adjustments'] += 1
                    elif shape_type in ("chart_title", "axis_title"):
                        element.text = str(new_text)
                        successful_updates += 1
                    elif shape_type == "smart_art":
                        element.text = str(new_text)
                        successful_updates += 1
                else:
                    if isinstance(new_text, (list, tuple)):
                        new_text = '\n'.join(str(t) for t in new_text)

                    if use_smart_adjustment:
                        # 使用智能调整并记录结果
                        original_text = shape.text_frame.text if hasattr(shape, 'text_frame') else ""
                        update_shape_text(shape, str(new_text), context)

                        # 记录调整统计
                        adjustment_stats['total_adjustments'] += 1
                        # 这里可以进一步记录具体的调整结果
                    else:
                        update_shape_text_legacy(shape, str(new_text))

                    successful_updates += 1

            except Exception as e:
                failed_updates += 1
                error_info = error_handler.handle_error(e, {
                    'shape_index': i,
                    'shape_type': type(shape).__name__,
                    'text_preview': str(new_text)[:50] + '...' if len(str(new_text)) > 50 else str(new_text),
                    'context': context
                })
                log.error(f"更新第 {i+1} 个形状失败: {error_info.message}")

        # 记录更新统计
        log.info(f"内容更新完成: 成功 {successful_updates}, 失败 {failed_updates}")
        if adjustment_stats['total_adjustments'] > 0:
            log.info(f"文本框调整统计: {adjustment_stats}")

        # 保存翻译后的文件
        try:
            prs.save(output_file)
            transaction.commit()
            log.info(f"PowerPoint文件翻译完成: {output_file}")

            # 输出健康报告
            health_status = translator.get_translation_health_status()
            log.info(f"翻译系统健康状态: {health_status['status']} (分数: {health_status['health_score']})")

        except Exception as e:
            log.error(f"保存文件失败: {str(e)}")
            transaction.rollback()
            if os.path.exists(output_file):
                try:
                    os.remove(output_file)
                except:
                    pass
            raise

    except Exception as e:
        error_info = error_handler.handle_error(e, {
            'input_file': input_file,
            'output_file': output_file,
            'total_texts': len(all_texts) if 'all_texts' in locals() else 0
        })
        log.error(f"PowerPoint翻译失败: {error_info.message}")
        transaction.rollback()
        raise