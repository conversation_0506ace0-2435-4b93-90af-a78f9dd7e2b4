import asyncio
import concurrent.futures
import json
import math
import re
import statistics
import time
import traceback
from collections import deque, defaultdict
from dataclasses import dataclass, field
from datetime import datetime
from enum import Enum
from pathlib import Path
from typing import Union, List, Any, Dict, Optional, Tuple, Callable

import httpx
import openai
from openai import OpenAI
from tenacity import (
    retry_if_exception_type,
    stop_after_attempt,
    wait_exponential,
    retry,
    retry_if_result,
    stop_after_delay,
    before_sleep_log
)

from core.setting import tool_settings
from core.log import setup_logger
logger = setup_logger(__name__)


class ErrorType(Enum):
    """错误类型枚举"""
    NETWORK_ERROR = "network_error"
    API_ERROR = "api_error"
    RATE_LIMIT_ERROR = "rate_limit_error"
    AUTHENTICATION_ERROR = "authentication_error"
    FILE_ERROR = "file_error"
    FORMAT_ERROR = "format_error"
    STYLE_ERROR = "style_error"
    BUSINESS_LOGIC_ERROR = "business_logic_error"
    UNKNOWN_ERROR = "unknown_error"


class ErrorSeverity(Enum):
    """错误严重程度"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


class RecoveryStrategy(Enum):
    """恢复策略"""
    RETRY = "retry"
    SKIP = "skip"
    USE_ORIGINAL = "use_original"
    USE_FALLBACK = "use_fallback"
    ABORT = "abort"
    USER_INTERVENTION = "user_intervention"


@dataclass
class ErrorInfo:
    """错误信息数据类"""
    error_type: ErrorType
    severity: ErrorSeverity
    message: str
    original_exception: Optional[Exception] = None
    context: Dict[str, Any] = field(default_factory=dict)
    timestamp: datetime = field(default_factory=datetime.now)
    stack_trace: Optional[str] = None
    recovery_strategy: Optional[RecoveryStrategy] = None
    retry_count: int = 0

    def __post_init__(self):
        if self.original_exception and not self.stack_trace:
            self.stack_trace = traceback.format_exc()


@dataclass
class ErrorStatistics:
    """错误统计数据"""
    total_errors: int = 0
    error_by_type: Dict[ErrorType, int] = field(default_factory=lambda: defaultdict(int))
    error_by_severity: Dict[ErrorSeverity, int] = field(default_factory=lambda: defaultdict(int))
    recent_errors: List[ErrorInfo] = field(default_factory=list)
    error_rate: float = 0.0
    last_error_time: Optional[datetime] = None

    def add_error(self, error_info: ErrorInfo):
        """添加错误记录"""
        self.total_errors += 1
        self.error_by_type[error_info.error_type] += 1
        self.error_by_severity[error_info.severity] += 1
        self.recent_errors.append(error_info)
        self.last_error_time = error_info.timestamp

        # 只保留最近100个错误
        if len(self.recent_errors) > 100:
            self.recent_errors = self.recent_errors[-100:]


class TextType(Enum):
    """文本类型枚举"""
    TITLE = "title"
    PARAGRAPH = "paragraph"
    TABLE_CELL = "table_cell"
    HEADER = "header"
    FOOTER = "footer"
    LIST_ITEM = "list_item"
    CHART_LABEL = "chart_label"
    OTHER = "other"


@dataclass
class TextItem:
    """文本项数据类"""
    text: str
    text_type: TextType = TextType.OTHER
    priority: int = 0  # 优先级，数字越小优先级越高
    estimated_tokens: int = 0
    complexity_score: float = 0.0
    original_index: int = 0


@dataclass
class BatchMetrics:
    """批处理性能指标"""
    batch_size: int
    total_tokens: int
    processing_time: float
    success_rate: float
    retry_count: int = 0
    error_messages: List[str] = field(default_factory=list)


@dataclass
class BatchConfig:
    """批处理配置"""
    max_tokens_per_batch: int = 3000  # 每批次最大token数
    min_batch_size: int = 1
    max_batch_size: int = 50
    target_batch_size: int = 10
    complexity_threshold: float = 0.7  # 复杂度阈值
    retry_max_attempts: int = 3
    retry_backoff_factor: float = 2.0


class RateLimiter:
    """速率限制器，控制每秒请求数量"""

    def __init__(self, qps: int):
        self.qps = qps
        self.requests = deque()
        self._lock = asyncio.Lock() if asyncio.iscoroutinefunction(self.__init__) else None

    def acquire(self):
        """获取请求许可（同步版本）"""
        now = time.time()

        # 清理超过1秒的请求记录
        while self.requests and now - self.requests[0] > 1.0:
            self.requests.popleft()

        # 如果当前秒内请求数已达到限制，等待
        if len(self.requests) >= self.qps:
            sleep_time = 1.0 - (now - self.requests[0])
            if sleep_time > 0:
                time.sleep(sleep_time)
                now = time.time()

        # 记录当前请求时间
        self.requests.append(now)


class ErrorHandler:
    """统一错误处理管理器"""

    def __init__(self, log_file: Optional[str] = None):
        self.statistics = ErrorStatistics()
        self.log_file = log_file or "translation_errors.log"
        self.error_threshold = 0.1  # 错误率阈值 10%
        self.total_operations = 0
        self.recovery_strategies = self._init_recovery_strategies()

    def _init_recovery_strategies(self) -> Dict[ErrorType, RecoveryStrategy]:
        """初始化错误恢复策略"""
        return {
            ErrorType.NETWORK_ERROR: RecoveryStrategy.RETRY,
            ErrorType.API_ERROR: RecoveryStrategy.RETRY,
            ErrorType.RATE_LIMIT_ERROR: RecoveryStrategy.RETRY,
            ErrorType.AUTHENTICATION_ERROR: RecoveryStrategy.ABORT,
            ErrorType.FILE_ERROR: RecoveryStrategy.SKIP,
            ErrorType.FORMAT_ERROR: RecoveryStrategy.USE_ORIGINAL,
            ErrorType.STYLE_ERROR: RecoveryStrategy.USE_ORIGINAL,
            ErrorType.BUSINESS_LOGIC_ERROR: RecoveryStrategy.USE_ORIGINAL,
            ErrorType.UNKNOWN_ERROR: RecoveryStrategy.USE_ORIGINAL,
        }

    def classify_error(self, exception: Exception) -> ErrorType:
        """分类错误类型"""
        if isinstance(exception, (ConnectionError, TimeoutError, httpx.ConnectError, httpx.TimeoutException)):
            return ErrorType.NETWORK_ERROR
        elif isinstance(exception, openai.RateLimitError):
            return ErrorType.RATE_LIMIT_ERROR
        elif isinstance(exception, openai.AuthenticationError):
            return ErrorType.AUTHENTICATION_ERROR
        elif isinstance(exception, (openai.APIError, openai.BadRequestError)):
            return ErrorType.API_ERROR
        elif isinstance(exception, (FileNotFoundError, PermissionError, OSError)):
            return ErrorType.FILE_ERROR
        elif isinstance(exception, (ValueError, TypeError)) and "format" in str(exception).lower():
            return ErrorType.FORMAT_ERROR
        else:
            return ErrorType.UNKNOWN_ERROR

    def determine_severity(self, error_type: ErrorType, context: Dict[str, Any] = None) -> ErrorSeverity:
        """确定错误严重程度"""
        severity_map = {
            ErrorType.AUTHENTICATION_ERROR: ErrorSeverity.CRITICAL,
            ErrorType.FILE_ERROR: ErrorSeverity.HIGH,
            ErrorType.RATE_LIMIT_ERROR: ErrorSeverity.MEDIUM,
            ErrorType.NETWORK_ERROR: ErrorSeverity.MEDIUM,
            ErrorType.API_ERROR: ErrorSeverity.MEDIUM,
            ErrorType.FORMAT_ERROR: ErrorSeverity.LOW,
            ErrorType.STYLE_ERROR: ErrorSeverity.LOW,
            ErrorType.BUSINESS_LOGIC_ERROR: ErrorSeverity.LOW,
            ErrorType.UNKNOWN_ERROR: ErrorSeverity.MEDIUM,
        }

        base_severity = severity_map.get(error_type, ErrorSeverity.MEDIUM)

        # 根据上下文调整严重程度
        if context:
            if context.get('batch_size', 0) > 10:
                # 大批次失败更严重
                if base_severity == ErrorSeverity.LOW:
                    base_severity = ErrorSeverity.MEDIUM
                elif base_severity == ErrorSeverity.MEDIUM:
                    base_severity = ErrorSeverity.HIGH

        return base_severity

    def handle_error(self, exception: Exception, context: Dict[str, Any] = None) -> ErrorInfo:
        """处理错误并返回错误信息"""
        error_type = self.classify_error(exception)
        severity = self.determine_severity(error_type, context)
        recovery_strategy = self.recovery_strategies.get(error_type, RecoveryStrategy.USE_ORIGINAL)

        error_info = ErrorInfo(
            error_type=error_type,
            severity=severity,
            message=str(exception),
            original_exception=exception,
            context=context or {},
            recovery_strategy=recovery_strategy
        )

        # 记录错误
        self.record_error(error_info)

        # 检查错误率
        self._check_error_rate()

        return error_info

    def record_error(self, error_info: ErrorInfo):
        """记录错误信息"""
        self.statistics.add_error(error_info)

        # 写入日志文件
        self._write_error_log(error_info)

        # 根据严重程度记录日志
        if error_info.severity == ErrorSeverity.CRITICAL:
            logger.critical(f"严重错误: {error_info.message}")
        elif error_info.severity == ErrorSeverity.HIGH:
            logger.error(f"高级错误: {error_info.message}")
        elif error_info.severity == ErrorSeverity.MEDIUM:
            logger.warning(f"中级错误: {error_info.message}")
        else:
            logger.info(f"低级错误: {error_info.message}")

    def _write_error_log(self, error_info: ErrorInfo):
        """写入错误日志文件"""
        try:
            log_entry = {
                "timestamp": error_info.timestamp.isoformat(),
                "error_type": error_info.error_type.value,
                "severity": error_info.severity.value,
                "message": error_info.message,
                "context": error_info.context,
                "stack_trace": error_info.stack_trace,
                "recovery_strategy": error_info.recovery_strategy.value if error_info.recovery_strategy else None,
                "retry_count": error_info.retry_count
            }

            with open(self.log_file, 'a', encoding='utf-8') as f:
                f.write(json.dumps(log_entry, ensure_ascii=False) + '\n')

        except Exception as e:
            logger.error(f"写入错误日志失败: {str(e)}")

    def _check_error_rate(self):
        """检查错误率并发出预警"""
        self.total_operations += 1
        current_error_rate = self.statistics.total_errors / self.total_operations
        self.statistics.error_rate = current_error_rate

        if current_error_rate > self.error_threshold:
            logger.warning(f"错误率过高: {current_error_rate:.2%} (阈值: {self.error_threshold:.2%})")

    def get_error_summary(self) -> Dict[str, Any]:
        """获取错误摘要"""
        return {
            "total_errors": self.statistics.total_errors,
            "total_operations": self.total_operations,
            "error_rate": self.statistics.error_rate,
            "errors_by_type": {k.value: v for k, v in self.statistics.error_by_type.items()},
            "errors_by_severity": {k.value: v for k, v in self.statistics.error_by_severity.items()},
            "last_error_time": self.statistics.last_error_time.isoformat() if self.statistics.last_error_time else None,
            "recent_error_count": len(self.statistics.recent_errors)
        }

    def should_retry(self, error_info: ErrorInfo) -> bool:
        """判断是否应该重试"""
        return error_info.recovery_strategy == RecoveryStrategy.RETRY and error_info.retry_count < 3

    def export_error_report(self, output_file: str = None) -> str:
        """导出错误报告"""
        if not output_file:
            output_file = f"error_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"

        report = {
            "generated_at": datetime.now().isoformat(),
            "summary": self.get_error_summary(),
            "recent_errors": [
                {
                    "timestamp": err.timestamp.isoformat(),
                    "type": err.error_type.value,
                    "severity": err.severity.value,
                    "message": err.message,
                    "context": err.context,
                    "retry_count": err.retry_count
                }
                for err in self.statistics.recent_errors[-20:]  # 最近20个错误
            ]
        }

        try:
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(report, f, ensure_ascii=False, indent=2)
            logger.info(f"错误报告已导出到: {output_file}")
            return output_file
        except Exception as e:
            logger.error(f"导出错误报告失败: {str(e)}")
            return ""


class EnhancedRetryManager:
    """增强的重试管理器"""

    def __init__(self, error_handler: ErrorHandler):
        self.error_handler = error_handler

    def create_retry_decorator(self,
                             max_attempts: int = 3,
                             max_wait_time: int = 300,  # 5分钟总超时
                             base_wait: float = 1.0,
                             max_wait: float = 60.0):
        """创建智能重试装饰器"""

        def is_retryable_error(exception):
            """判断是否为可重试错误"""
            error_info = self.error_handler.handle_error(exception)
            return self.error_handler.should_retry(error_info)

        def retry_if_retryable(result):
            """基于结果判断是否重试"""
            if isinstance(result, Exception):
                return is_retryable_error(result)
            return False

        def before_sleep_callback(retry_state):
            """重试前的回调"""
            exception = retry_state.outcome.exception()
            if exception:
                error_info = self.error_handler.handle_error(exception)
                error_info.retry_count = retry_state.attempt_number
                logger.warning(
                    f"重试 {retry_state.attempt_number}/{max_attempts}: "
                    f"{error_info.error_type.value} - {error_info.message}, "
                    f"等待 {retry_state.next_action.sleep:.1f} 秒"
                )

        return retry(
            retry=retry_if_exception_type((
                ConnectionError,
                TimeoutError,
                openai.RateLimitError,
                openai.APIError,
                httpx.ConnectError,
                httpx.TimeoutException
            )),
            stop=stop_after_attempt(max_attempts) | stop_after_delay(max_wait_time),
            wait=wait_exponential(multiplier=base_wait, min=base_wait, max=max_wait),
            before_sleep=before_sleep_callback,
            reraise=True
        )

    def execute_with_fallback(self,
                            primary_func: Callable,
                            fallback_func: Callable = None,
                            context: Dict[str, Any] = None) -> Any:
        """执行函数，失败时使用降级策略"""
        try:
            # 尝试主要函数
            return primary_func()
        except Exception as e:
            error_info = self.error_handler.handle_error(e, context)

            # 根据恢复策略处理
            if error_info.recovery_strategy == RecoveryStrategy.USE_FALLBACK and fallback_func:
                # logger.info(f"使用降级策略: {error_info.error_type.value}")
                try:
                    return fallback_func()
                except Exception as fallback_error:
                    # logger.error(f"降级策略也失败: {str(fallback_error)}")
                    # 降级策略失败，使用原文
                    return context.get('original_text', '') if context else ''
            elif error_info.recovery_strategy == RecoveryStrategy.USE_ORIGINAL:
                # logger.info(f"使用原文策略: {error_info.error_type.value}")
                return context.get('original_text', '') if context else ''
            elif error_info.recovery_strategy == RecoveryStrategy.SKIP:
                # logger.info(f"跳过处理: {error_info.error_type.value}")
                return None
            elif error_info.recovery_strategy == RecoveryStrategy.RETRY:
                # 对于网络错误等，如果有降级函数就使用，否则使用原文
                if fallback_func:
                    # logger.info(f"重试失败，使用降级策略: {error_info.error_type.value}")
                    try:
                        return fallback_func()
                    except Exception:
                        return context.get('original_text', '') if context else ''
                else:
                    # logger.info(f"重试失败，使用原文: {error_info.error_type.value}")
                    return context.get('original_text', '') if context else ''
            else:
                raise e


class TransactionManager:
    """事务管理器，支持回滚操作"""

    def __init__(self):
        self.operations = []
        self.rollback_functions = []

    def add_operation(self, operation_name: str, rollback_func: Callable = None):
        """添加操作和对应的回滚函数"""
        self.operations.append(operation_name)
        if rollback_func:
            self.rollback_functions.append(rollback_func)

    def rollback(self):
        """执行回滚操作"""
        # logger.info(f"开始回滚 {len(self.rollback_functions)} 个操作")

        # 逆序执行回滚
        for i, rollback_func in enumerate(reversed(self.rollback_functions)):
            try:
                rollback_func()
                # logger.debug(f"回滚操作 {len(self.rollback_functions) - i} 成功")
            except Exception as e:
                logger.error(f"回滚操作失败: {str(e)}")

        self.operations.clear()
        self.rollback_functions.clear()

    def commit(self):
        """提交事务，清理回滚函数"""
        # logger.debug(f"提交事务，包含 {len(self.operations)} 个操作")
        self.operations.clear()
        self.rollback_functions.clear()


class SmartBatchProcessor:
    """智能批处理管理器"""

    def __init__(self, config: Optional[BatchConfig] = None):
        self.config = config or BatchConfig()
        self.metrics_history: List[BatchMetrics] = []
        self.optimal_batch_sizes: Dict[TextType, int] = {}
        self.performance_stats = defaultdict(list)

    def estimate_tokens(self, text: str) -> int:
        """估算文本的token数量"""
        # 简化的token估算：中文字符*1.5 + 英文单词*1.3 + 标点符号*0.5
        chinese_chars = sum(1 for c in text if '\u4e00' <= c <= '\u9fff')
        english_words = len([w for w in text.split() if any(c.isalpha() for c in w)])
        punctuation = sum(1 for c in text if not c.isalnum() and not c.isspace())

        estimated = int(chinese_chars * 1.5 + english_words * 1.3 + punctuation * 0.5)
        return max(estimated, len(text) // 4)  # 最少按字符数的1/4计算

    def calculate_complexity(self, text: str) -> float:
        """计算文本复杂度"""
        if not text.strip():
            return 0.0

        # 复杂度因子
        factors = {
            'length': min(len(text) / 1000, 1.0),  # 长度因子
            'chinese_ratio': sum(1 for c in text if '\u4e00' <= c <= '\u9fff') / len(text),
            'punctuation_density': sum(1 for c in text if not c.isalnum() and not c.isspace()) / len(text),
            'special_chars': sum(1 for c in text if ord(c) > 127) / len(text),
            'line_breaks': text.count('\n') / max(len(text), 1),
        }

        # 加权计算复杂度
        complexity = (
            factors['length'] * 0.3 +
            factors['chinese_ratio'] * 0.2 +
            factors['punctuation_density'] * 0.2 +
            factors['special_chars'] * 0.15 +
            factors['line_breaks'] * 0.15
        )

        return min(complexity, 1.0)

    def classify_text_type(self, text: str, context_hint: str = "") -> TextType:
        """根据文本内容和上下文提示分类文本类型"""
        text_lower = text.lower().strip()

        # 根据上下文提示
        if context_hint:
            context_lower = context_hint.lower()
            if 'title' in context_lower or 'heading' in context_lower:
                return TextType.TITLE
            elif 'table' in context_lower or 'cell' in context_lower:
                return TextType.TABLE_CELL
            elif 'header' in context_lower:
                return TextType.HEADER
            elif 'footer' in context_lower:
                return TextType.FOOTER
            elif 'chart' in context_lower or 'label' in context_lower:
                return TextType.CHART_LABEL

        # 根据文本特征分类
        if len(text_lower) < 50 and ('\n' not in text or text.count('\n') <= 1):
            if any(keyword in text_lower for keyword in ['第', '章', 'chapter', 'section', '标题']):
                return TextType.TITLE
            elif len(text_lower) < 20:
                return TextType.CHART_LABEL

        if '\n' in text and len(text) > 100:
            return TextType.PARAGRAPH

        if len(text) < 100 and not '\n' in text:
            return TextType.TABLE_CELL

        return TextType.OTHER

    def create_text_items(self, texts: List[str], context_hints: List[str] = None) -> List[TextItem]:
        """创建文本项列表"""
        if context_hints is None:
            context_hints = [""] * len(texts)

        text_items = []
        for i, (text, hint) in enumerate(zip(texts, context_hints)):
            text_type = self.classify_text_type(text, hint)
            estimated_tokens = self.estimate_tokens(text)
            complexity = self.calculate_complexity(text)

            # 根据文本类型设置优先级
            priority_map = {
                TextType.TITLE: 1,
                TextType.HEADER: 2,
                TextType.FOOTER: 2,
                TextType.PARAGRAPH: 3,
                TextType.TABLE_CELL: 4,
                TextType.LIST_ITEM: 4,
                TextType.CHART_LABEL: 5,
                TextType.OTHER: 6
            }

            text_items.append(TextItem(
                text=text,
                text_type=text_type,
                priority=priority_map.get(text_type, 6),
                estimated_tokens=estimated_tokens,
                complexity_score=complexity,
                original_index=i
            ))

        return text_items

    def optimize_batch_size(self, text_items: List[TextItem], text_type: TextType) -> int:
        """根据历史性能数据优化批次大小"""
        # 如果有历史最优数据，使用它
        if text_type in self.optimal_batch_sizes:
            base_size = self.optimal_batch_sizes[text_type]
        else:
            base_size = self.config.target_batch_size

        # 根据文本复杂度调整
        avg_complexity = statistics.mean([item.complexity_score for item in text_items]) if text_items else 0.5

        if avg_complexity > self.config.complexity_threshold:
            # 高复杂度文本，减小批次
            adjusted_size = max(self.config.min_batch_size, int(base_size * 0.7))
        else:
            # 低复杂度文本，可以增大批次
            adjusted_size = min(self.config.max_batch_size, int(base_size * 1.3))

        return adjusted_size

    def create_smart_batches(self, text_items: List[TextItem]) -> List[List[TextItem]]:
        """创建智能批次"""
        if not text_items:
            return []

        # 按类型和优先级分组
        type_groups = defaultdict(list)
        for item in text_items:
            type_groups[item.text_type].append(item)

        # 对每个组内按优先级排序
        for text_type in type_groups:
            type_groups[text_type].sort(key=lambda x: (x.priority, x.complexity_score))

        batches = []

        # 为每种文本类型创建批次
        for text_type, items in type_groups.items():
            optimal_size = self.optimize_batch_size(items, text_type)
            type_batches = self._create_token_aware_batches(items, optimal_size)
            batches.extend(type_batches)

        return batches

    def _create_token_aware_batches(self, items: List[TextItem], target_size: int) -> List[List[TextItem]]:
        """创建考虑token限制的批次"""
        batches = []
        current_batch = []
        current_tokens = 0

        for item in items:
            # 检查是否超过token限制或批次大小限制
            if (current_tokens + item.estimated_tokens > self.config.max_tokens_per_batch or
                len(current_batch) >= target_size) and current_batch:

                batches.append(current_batch)
                current_batch = []
                current_tokens = 0

            current_batch.append(item)
            current_tokens += item.estimated_tokens

        # 添加最后一个批次
        if current_batch:
            batches.append(current_batch)

        return batches

    def process_batch_with_retry(self, batch: List[TextItem], translator_func) -> Tuple[List[str], BatchMetrics]:
        """带重试机制的批次处理"""
        start_time = time.time()
        texts = [item.text for item in batch]
        total_tokens = sum(item.estimated_tokens for item in batch)

        metrics = BatchMetrics(
            batch_size=len(batch),
            total_tokens=total_tokens,
            processing_time=0.0,
            success_rate=0.0
        )

        for attempt in range(self.config.retry_max_attempts):
            try:
                # logger.debug(f"处理批次 (尝试 {attempt + 1}/{self.config.retry_max_attempts}): "
                #            f"{len(batch)} 个文本, {total_tokens} tokens")

                # 执行翻译
                results = translator_func(texts)

                # 成功
                metrics.processing_time = time.time() - start_time
                metrics.success_rate = 1.0
                metrics.retry_count = attempt

                # 记录成功的性能数据
                self._record_batch_performance(batch, metrics)

                return results, metrics

            except Exception as e:
                error_msg = str(e)
                metrics.error_messages.append(error_msg)
                # logger.warning(f"批次处理失败 (尝试 {attempt + 1}): {error_msg}")

                # 如果是最后一次尝试，或者是不可重试的错误
                if attempt == self.config.retry_max_attempts - 1:
                    # 尝试拆分批次重试
                    if len(batch) > 1:
                        # logger.info(f"尝试拆分批次重试: {len(batch)} -> {len(batch)//2}")
                        return self._retry_with_smaller_batches(batch, translator_func, metrics)
                    else:
                        # 单个文本也失败，返回原文
                        metrics.processing_time = time.time() - start_time
                        metrics.success_rate = 0.0
                        metrics.retry_count = attempt + 1
                        return [item.text for item in batch], metrics

                # 指数退避
                wait_time = self.config.retry_backoff_factor ** attempt
                # logger.debug(f"等待 {wait_time} 秒后重试...")
                time.sleep(wait_time)

        # 不应该到达这里
        return [item.text for item in batch], metrics

    def _retry_with_smaller_batches(self, batch: List[TextItem], translator_func, original_metrics: BatchMetrics) -> Tuple[List[str], BatchMetrics]:
        """用更小的批次重试"""
        # 拆分为更小的批次
        mid = len(batch) // 2
        smaller_batches = [batch[:mid], batch[mid:]]

        all_results = []
        total_retry_count = original_metrics.retry_count
        all_errors = original_metrics.error_messages.copy()

        for small_batch in smaller_batches:
            try:
                results, sub_metrics = self.process_batch_with_retry(small_batch, translator_func)
                all_results.extend(results)
                total_retry_count += sub_metrics.retry_count
                all_errors.extend(sub_metrics.error_messages)
            except Exception as e:
                # 如果小批次也失败，返回原文
                all_results.extend([item.text for item in small_batch])
                all_errors.append(f"小批次失败: {str(e)}")

        # 更新指标
        original_metrics.retry_count = total_retry_count
        original_metrics.error_messages = all_errors
        original_metrics.success_rate = 0.5  # 部分成功

        return all_results, original_metrics

    def _record_batch_performance(self, batch: List[TextItem], metrics: BatchMetrics):
        """记录批次性能数据"""
        self.metrics_history.append(metrics)

        # 更新最优批次大小
        if metrics.success_rate > 0.8:  # 成功率高的批次
            text_types = set(item.text_type for item in batch)
            for text_type in text_types:
                if text_type not in self.optimal_batch_sizes:
                    self.optimal_batch_sizes[text_type] = metrics.batch_size
                else:
                    # 使用移动平均更新
                    current = self.optimal_batch_sizes[text_type]
                    self.optimal_batch_sizes[text_type] = int(current * 0.8 + metrics.batch_size * 0.2)

        # 记录性能统计
        self.performance_stats['processing_times'].append(metrics.processing_time)
        self.performance_stats['success_rates'].append(metrics.success_rate)
        self.performance_stats['batch_sizes'].append(metrics.batch_size)

    def get_performance_summary(self) -> Dict[str, Any]:
        """获取性能摘要"""
        if not self.metrics_history:
            return {"message": "暂无性能数据"}

        recent_metrics = self.metrics_history[-10:]  # 最近10个批次

        return {
            "total_batches": len(self.metrics_history),
            "recent_avg_processing_time": statistics.mean([m.processing_time for m in recent_metrics]),
            "recent_avg_success_rate": statistics.mean([m.success_rate for m in recent_metrics]),
            "optimal_batch_sizes": dict(self.optimal_batch_sizes),
            "total_errors": sum(len(m.error_messages) for m in self.metrics_history),
            "avg_batch_size": statistics.mean([m.batch_size for m in recent_metrics]),
        }


class OpenaiTranslate:
    def __init__(self):
        self.client = OpenAI(
            api_key=tool_settings.llm_config.api_key,
            base_url=tool_settings.llm_config.base_url,
            http_client = httpx.Client(
                limits=httpx.Limits(
                    max_connections=None, max_keepalive_connections=None
                ),
                # 忽略ssl
                verify=False,
        ),
        )
        # 初始化速率限制器
        self.rate_limiter = RateLimiter(tool_settings.tool_config.qps)
        # 初始化智能批处理管理器
        self.batch_processor = SmartBatchProcessor()
        # 初始化错误处理管理器
        self.error_handler = ErrorHandler()
        # 初始化重试管理器
        self.retry_manager = EnhancedRetryManager(self.error_handler)
        # 创建智能重试装饰器
        self.smart_retry = self.retry_manager.create_retry_decorator()

    def translate_smart_batch(self, texts: List[str], context_hints: List[str] = None) -> List[str]:
        """使用智能批处理策略翻译文本列表"""
        if not texts:
            return []

        if len(texts) == 1:
            # 单个文本直接翻译
            return [self.translate_single(texts[0])]

        # logger.info(f"开始智能批处理翻译 {len(texts)} 个文本")

        # 创建文本项
        text_items = self.batch_processor.create_text_items(texts, context_hints)

        # 创建智能批次
        batches = self.batch_processor.create_smart_batches(text_items)

        # logger.info(f"创建了 {len(batches)} 个智能批次")

        # 处理所有批次
        all_results = [None] * len(texts)  # 预分配结果列表
        total_processing_time = 0
        successful_batches = 0

        for i, batch in enumerate(batches):
            # logger.debug(f"处理批次 {i+1}/{len(batches)}: {len(batch)} 个文本")

            # 处理批次
            batch_results, metrics = self.batch_processor.process_batch_with_retry(
                batch, self._translate_batch_texts
            )

            # 将结果放回原始位置
            for item, result in zip(batch, batch_results):
                all_results[item.original_index] = result

            total_processing_time += metrics.processing_time
            if metrics.success_rate > 0.5:
                successful_batches += 1

            # logger.debug(f"批次 {i+1} 完成: 成功率 {metrics.success_rate:.2f}, "
            #             f"用时 {metrics.processing_time:.2f}s")

        # 记录总体性能
        overall_success_rate = successful_batches / len(batches) if batches else 0
        # logger.info(f"智能批处理完成: 总用时 {total_processing_time:.2f}s, "
        #            f"成功率 {overall_success_rate:.2f}")

        # 打印性能摘要
        performance_summary = self.batch_processor.get_performance_summary()
        # logger.debug(f"批处理性能摘要: {performance_summary}")

        return all_results

    def _translate_batch_texts(self, texts: List[str]) -> List[str]:
        """翻译一批文本（内部方法），使用增强错误处理"""
        results = []
        failed_indices = []

        for i, text in enumerate(texts):
            try:
                context = {
                    'original_text': text,
                    'batch_index': i,
                    'batch_size': len(texts)
                }
                result = self.translate_single(text, context)
                results.append(result)
            except Exception as e:
                # 记录失败的索引
                failed_indices.append(i)
                error_info = self.error_handler.handle_error(e, {
                    'batch_index': i,
                    'batch_size': len(texts),
                    'text_preview': text[:50] + '...' if len(text) > 50 else text
                })

                # 根据恢复策略处理
                if error_info.recovery_strategy == RecoveryStrategy.USE_ORIGINAL:
                    results.append(text)
                elif error_info.recovery_strategy == RecoveryStrategy.SKIP:
                    results.append("")
                else:
                    results.append(text)  # 默认使用原文

                # logger.warning(f"批次中第 {i+1} 个文本翻译失败，使用恢复策略: {error_info.recovery_strategy.value}")

        # 如果有失败的文本，记录批次级别的错误
        if failed_indices:
            batch_error_context = {
                'failed_count': len(failed_indices),
                'total_count': len(texts),
                'failure_rate': len(failed_indices) / len(texts),
                'failed_indices': failed_indices
            }
            # logger.warning(f"批次部分失败: {len(failed_indices)}/{len(texts)} 个文本失败")

        return results

    def translate_single(self, text: str, context: Dict[str, Any] = None) -> str:
        """翻译单个文本，使用增强错误处理"""
        if not text or not text.strip():
            return text

        context = context or {'original_text': text}
        context['text_length'] = len(text)

        def _do_translate():
            # 应用速率限制
            self.rate_limiter.acquire()

            response = self.client.chat.completions.create(
                model=tool_settings.llm_config.model,
                messages=[
                    {
                        "role": "system",
                        "content": tool_settings.llm_config.translate_prompt,
                    },
                    {
                        "role": "user",
                        "content": text,
                    },
                ],
                extra_body={
                    "chat_template_kwargs": {"enable_thinking": False},
                },
            )
            content = self._remove_cot_content(response.choices[0].message.content or "")

            # 简单的翻译质量检测
            # if not content or content.strip() == text.strip():
            #     logger.warning(f"翻译质量异常: 输出与输入相同")

            return content

        def _fallback_translate():
            """降级翻译策略"""
            # logger.info("使用降级翻译策略")
            # 这里可以集成其他翻译服务作为备用
            return text  # 暂时返回原文

        try:
            # 使用重试管理器执行翻译
            return self.retry_manager.execute_with_fallback(
                primary_func=lambda: self.smart_retry(_do_translate)(),
                fallback_func=_fallback_translate,
                context=context
            )
        except Exception as e:
            # 最终错误处理
            error_info = self.error_handler.handle_error(e, context)
            logger.error(f"翻译最终失败: {error_info.message}")
            return text  # 返回原文

    @retry(
        retry=retry_if_exception_type(openai.RateLimitError),
        stop=stop_after_attempt(100),
        wait=wait_exponential(multiplier=1, min=1, max=15),
        before_sleep=lambda retry_state: logger.warning(
            f"RateLimitError, retrying in {retry_state.next_action.sleep} seconds... "
            f"(Attempt {retry_state.attempt_number}/100)"
        ),
    )
    def translate(self, text: Union[str, List[str]], use_smart_batch: bool = True, context_hints: List[str] = None) -> list[Any] | list[None] | str | None | Any:
        """
        翻译文本

        Args:
            text: 要翻译的文本或文本列表
            use_smart_batch: 是否使用智能批处理（默认True）
            context_hints: 上下文提示列表，用于优化批处理策略
        """
        # 定义单个文本的翻译函数
        def translate_single(t):
            if not t:
                return t
            try:
                # 应用速率限制
                self.rate_limiter.acquire()

                response = self.client.chat.completions.create(
                    model=tool_settings.llm_config.model,
                    messages=[
                        {
                            "role": "system",
                            "content": tool_settings.llm_config.translate_prompt,
                        },
                        {
                            "role": "user",
                            "content": t,
                        },
                    ],
                    extra_body={
                        "chat_template_kwargs": {"enable_thinking": False},
                    },
                )
                content = self._remove_cot_content(response.choices[0].message.content or "")

                return content
            except Exception as e:
                logger.error(f"翻译失败: {str(e)}")
                return t  # 翻译失败时返回原文

        if isinstance(text, list):
            if not text:
                return []

            # 根据参数选择处理方式
            if use_smart_batch and len(text) > 1:
                # logger.info("使用智能批处理模式")
                return self.translate_smart_batch(text, context_hints)
            else:
                # logger.info("使用传统并发模式")
                return self._translate_concurrent_legacy(text, translate_single)

        elif isinstance(text, str):
            # if (max_token := len(text) * 5) > self.options["num_predict"]:
            #     self.options["num_predict"] = max_token
            return translate_single(text)
        return None

    def _translate_concurrent_legacy(self, text: List[str], translate_single_func) -> List[str]:
        """传统的并发翻译方法（向后兼容）"""
        # 使用线程池并发执行翻译，正确使用limits作为并发数
        results = [None] * len(text)  # 预分配结果列表
        max_workers = min(tool_settings.tool_config.limits, len(text))  # 避免创建过多线程

        # logger.info(f"开始传统批量翻译 {len(text)} 个文本，使用 {max_workers} 个并发线程，QPS限制: {tool_settings.tool_config.qps}")

        with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
            # 创建任务到索引的映射
            future_to_idx = {executor.submit(translate_single_func, t): i for i, t in enumerate(text)}

            # 收集结果，保持原始顺序
            for future in concurrent.futures.as_completed(future_to_idx):
                idx = future_to_idx[future]
                try:
                    results[idx] = future.result()
                except Exception as e:
                    logger.error(f"处理翻译结果时出错: {str(e)}")
                    results[idx] = text[idx]  # 出错时保留原文

        return results

    def get_translation_health_status(self) -> Dict[str, Any]:
        """获取翻译系统健康状态"""
        error_summary = self.error_handler.get_error_summary()
        batch_summary = self.batch_processor.get_performance_summary()

        # 计算健康分数 (0-100)
        health_score = 100

        # 错误率影响
        if error_summary['error_rate'] > 0.1:  # 10%
            health_score -= 30
        elif error_summary['error_rate'] > 0.05:  # 5%
            health_score -= 15

        # 批处理成功率影响
        if 'recent_avg_success_rate' in batch_summary:
            success_rate = batch_summary['recent_avg_success_rate']
            if success_rate < 0.8:
                health_score -= 20
            elif success_rate < 0.9:
                health_score -= 10

        # 确定健康状态
        if health_score >= 90:
            status = "excellent"
        elif health_score >= 70:
            status = "good"
        elif health_score >= 50:
            status = "fair"
        else:
            status = "poor"

        return {
            "health_score": max(0, health_score),
            "status": status,
            "error_summary": error_summary,
            "batch_summary": batch_summary,
            "recommendations": self._get_health_recommendations(error_summary, batch_summary)
        }

    def _get_health_recommendations(self, error_summary: Dict, batch_summary: Dict) -> List[str]:
        """获取健康改进建议"""
        recommendations = []

        if error_summary['error_rate'] > 0.1:
            recommendations.append("错误率过高，建议检查网络连接和API配置")

        if error_summary.get('errors_by_type', {}).get('rate_limit_error', 0) > 0:
            recommendations.append("存在速率限制错误，建议降低QPS设置")

        if error_summary.get('errors_by_type', {}).get('authentication_error', 0) > 0:
            recommendations.append("存在认证错误，请检查API密钥配置")

        if batch_summary.get('recent_avg_success_rate', 1.0) < 0.8:
            recommendations.append("批处理成功率较低，建议减小批次大小")

        if not recommendations:
            recommendations.append("系统运行良好，无需特别调整")

        return recommendations

    def export_health_report(self, output_file: str = None) -> str:
        """导出健康报告"""
        health_status = self.get_translation_health_status()

        if not output_file:
            output_file = f"translation_health_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"

        try:
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(health_status, f, ensure_ascii=False, indent=2)
            logger.info(f"健康报告已导出到: {output_file}")
            return output_file
        except Exception as e:
            # logger.error(f"导出健康报告失败: {str(e)}")
            return ""

    @staticmethod
    def _remove_cot_content(content: str) -> str:
        """Remove text content with the thought chain from the chat response

        :param content: Non-streaming text content
        :return: Text without a thought chain
        """
        con = re.sub(r"^<think>.+?</think>", "", content, count=1, flags=re.DOTALL)
        con = re.sub(r"</think>", "", con, count=1, flags=re.DOTALL)
        return con


openaiTranslate = OpenaiTranslate()